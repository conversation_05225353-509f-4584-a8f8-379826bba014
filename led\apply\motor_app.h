#ifndef MOTOR_APP_H
#define MOTOR_APP_H

#include "ti_msp_dl_config.h"

// PWM引脚定义（基于m0四元数项目配置）
#define right_pwm_channel_1    GPIO_PWM_0_C3_IDX  // TIMA0-CH3-PA4  右电机INA1
#define right_pwm_channel_2    GPIO_PWM_0_C2_IDX  // TIMA0-CH2-PA7  右电机INA2  
#define left_pwm_channel_1     GPIO_PWM_0_C1_IDX  // TIMA0-CH1-PA3  左电机INB1
#define left_pwm_channel_2     GPIO_PWM_0_C0_IDX  // TIMA0-CH0-PB14 左电机INB2

/**
 * @brief 电机应用层初始化
 */
void Motor_App_Init(void);

/**
 * @brief 左轮前进
 * @param speed 速度值 (0-999)
 */
void left_wheel_forward(int16_t speed);

/**
 * @brief 左轮后退
 * @param speed 速度值 (0-999)
 */
void left_wheel_backward(int16_t speed);

/**
 * @brief 右轮前进
 * @param speed 速度值 (0-999)
 */
void right_wheel_forward(int16_t speed);

/**
 * @brief 右轮后退
 * @param speed 速度值 (0-999)
 */
void right_wheel_backward(int16_t speed);

/**
 * @brief 整车停止
 */
void car_stop(void);

/**
 * @brief 整车运动控制
 * @param left_speed 左轮速度 (-999到999，负值表示后退)
 * @param right_speed 右轮速度 (-999到999，负值表示后退)
 */
void car_move(int16_t left_speed, int16_t right_speed);

/**
 * @brief 电机演示函数
 */
void motor_demo(void);

#endif
