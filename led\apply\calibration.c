#include "headfile.h"
#include "calibration.h"
#include <string.h>

// 全局变量
static calibration_data_t calib_data;           // 校准数据
static calib_status_t calib_status = CALIB_STATUS_NONE; // 校准状态
static uint8_t auto_save_enabled = 1;          // 自动保存使能
static uint32_t save_timer = 0;                // 保存计时器
static uint8_t data_changed = 0;               // 数据变更标志

// 配置参数
#define AUTO_SAVE_INTERVAL_MS 30000            // 自动保存间隔(30秒)
#define FLASH_CALIB_ADDRESS 0x1F000            // Flash存储地址(假设地址)

// CRC32查找表（简化版）
static const uint32_t crc32_table[16] = {
    0x00000000, 0x1DB71064, 0x3B6E20C8, 0x26D930AC,
    0x76DC4190, 0x6B6B51F4, 0x4DB26158, 0x5005713C,
    0xEDB88320, 0xF00F9344, 0xD6D6A3E8, 0xCB61B38C,
    0x9B64C2B0, 0x86D3D2D4, 0xA00AE278, 0xBDBDF21C
};

/***************************************
函数名: uint32_t Calculate_CRC32
说明: 计算CRC32校验值
参数: const uint8_t *data - 数据指针
      uint32_t length - 数据长度
返回: uint32_t CRC32值
***************************************/
static uint32_t Calculate_CRC32(const uint8_t *data, uint32_t length)
{
    uint32_t crc = 0xFFFFFFFF;
    
    for(uint32_t i = 0; i < length; i++)
    {
        uint8_t byte = data[i];
        crc = crc32_table[(crc ^ byte) & 0x0F] ^ (crc >> 4);
        crc = crc32_table[(crc ^ (byte >> 4)) & 0x0F] ^ (crc >> 4);
    }
    
    return ~crc;
}

/***************************************
函数名: void Calibration_Init
说明: 初始化校准数据管理
参数: 无
返回: 无
***************************************/
void Calibration_Init(void)
{
    // 初始化校准数据结构
    memset(&calib_data, 0, sizeof(calibration_data_t));
    
    // 设置默认值
    calib_data.magic_number = CALIB_MAGIC_NUMBER;
    calib_data.version = CALIB_DATA_VERSION;
    
    // 设置默认比例因子为1.0
    calib_data.gyro_scale.x = 1.0f;
    calib_data.gyro_scale.y = 1.0f;
    calib_data.gyro_scale.z = 1.0f;
    calib_data.accel_scale.x = 1.0f;
    calib_data.accel_scale.y = 1.0f;
    calib_data.accel_scale.z = 1.0f;
    
    // 设置参考温度
    calib_data.reference_temp = 25.0f;
    
    calib_status = CALIB_STATUS_NONE;
    save_timer = 0;
    data_changed = 0;
    
    // 尝试加载已保存的校准数据
    Calibration_Load();
}

/***************************************
函数名: uint8_t Calibration_Load
说明: 加载校准数据
参数: 无
返回: uint8_t 加载结果(0-成功, 1-失败)
***************************************/
uint8_t Calibration_Load(void)
{
    calib_status = CALIB_STATUS_LOADING;
    
    // 注意：这里使用模拟的Flash读取
    // 实际项目中需要根据具体MCU的Flash操作API进行实现
    
    // 模拟从Flash读取数据
    // 在实际实现中，这里应该是：
    // memcpy(&calib_data, (void*)FLASH_CALIB_ADDRESS, sizeof(calibration_data_t));
    
    // 由于无法实际操作Flash，这里模拟加载失败的情况
    // 实际项目中需要实现具体的Flash读取功能
    
    // 验证魔数
    if(calib_data.magic_number != CALIB_MAGIC_NUMBER)
    {
        calib_status = CALIB_STATUS_INVALID;
        return 1; // 数据无效
    }
    
    // 验证版本号
    if(calib_data.version != CALIB_DATA_VERSION)
    {
        calib_status = CALIB_STATUS_INVALID;
        return 1; // 版本不匹配
    }
    
    // 验证CRC32
    uint32_t calculated_crc = Calculate_CRC32((uint8_t*)&calib_data, 
                                             sizeof(calibration_data_t) - sizeof(uint32_t));
    if(calculated_crc != calib_data.crc32)
    {
        calib_status = CALIB_STATUS_INVALID;
        return 1; // CRC校验失败
    }
    
    calib_status = CALIB_STATUS_VALID;
    return 0; // 加载成功
}

/***************************************
函数名: uint8_t Calibration_Save
说明: 保存校准数据
参数: 无
返回: uint8_t 保存结果(0-成功, 1-失败)
***************************************/
uint8_t Calibration_Save(void)
{
    calib_status = CALIB_STATUS_SAVING;
    
    // 更新时间戳和使用次数
    calib_data.calib_timestamp++; // 简化的时间戳
    calib_data.usage_count++;
    
    // 计算CRC32
    calib_data.crc32 = Calculate_CRC32((uint8_t*)&calib_data, 
                                      sizeof(calibration_data_t) - sizeof(uint32_t));
    
    // 注意：这里使用模拟的Flash写入
    // 实际项目中需要根据具体MCU的Flash操作API进行实现
    
    // 模拟Flash写入操作
    // 在实际实现中，这里应该是Flash擦除和写入操作
    // 例如：Flash_Erase_Sector(FLASH_CALIB_ADDRESS);
    //      Flash_Write(FLASH_CALIB_ADDRESS, (uint8_t*)&calib_data, sizeof(calibration_data_t));
    
    calib_status = CALIB_STATUS_VALID;
    data_changed = 0;
    
    return 0; // 保存成功（模拟）
}

/***************************************
函数名: uint8_t Calibration_Reset
说明: 重置校准数据
参数: 无
返回: uint8_t 重置结果(0-成功, 1-失败)
***************************************/
uint8_t Calibration_Reset(void)
{
    // 重新初始化校准数据
    Calibration_Init();
    
    // 标记数据已更改
    data_changed = 1;
    
    // 立即保存重置后的数据
    return Calibration_Save();
}

/***************************************
函数名: calib_status_t Calibration_Get_Status
说明: 获取校准状态
参数: 无
返回: calib_status_t 校准状态
***************************************/
calib_status_t Calibration_Get_Status(void)
{
    return calib_status;
}

/***************************************
函数名: void Calibration_Set_Gyro_Offset
说明: 设置陀螺仪零偏
参数: vector3f offset - 零偏值
返回: 无
***************************************/
void Calibration_Set_Gyro_Offset(vector3f offset)
{
    calib_data.gyro_offset = offset;
    data_changed = 1;
}

/***************************************
函数名: vector3f Calibration_Get_Gyro_Offset
说明: 获取陀螺仪零偏
参数: 无
返回: vector3f 零偏值
***************************************/
vector3f Calibration_Get_Gyro_Offset(void)
{
    return calib_data.gyro_offset;
}

/***************************************
函数名: void Calibration_Set_Accel_Offset
说明: 设置加速度计零偏
参数: vector3f offset - 零偏值
返回: 无
***************************************/
void Calibration_Set_Accel_Offset(vector3f offset)
{
    calib_data.accel_offset = offset;
    data_changed = 1;
}

/***************************************
函数名: vector3f Calibration_Get_Accel_Offset
说明: 获取加速度计零偏
参数: 无
返回: vector3f 零偏值
***************************************/
vector3f Calibration_Get_Accel_Offset(void)
{
    return calib_data.accel_offset;
}

/***************************************
函数名: void Calibration_Set_Temp_Coeff
说明: 设置温度系数
参数: float gyro_coeff[3] - 陀螺仪温度系数
      float accel_coeff[3] - 加速度计温度系数
返回: 无
***************************************/
void Calibration_Set_Temp_Coeff(float gyro_coeff[3], float accel_coeff[3])
{
    for(int i = 0; i < 3; i++)
    {
        calib_data.temp_coeff_gyro[i] = gyro_coeff[i];
        calib_data.temp_coeff_accel[i] = accel_coeff[i];
    }
    data_changed = 1;
}

/***************************************
函数名: void Calibration_Get_Temp_Coeff
说明: 获取温度系数
参数: float gyro_coeff[3] - 陀螺仪温度系数输出
      float accel_coeff[3] - 加速度计温度系数输出
返回: 无
***************************************/
void Calibration_Get_Temp_Coeff(float gyro_coeff[3], float accel_coeff[3])
{
    for(int i = 0; i < 3; i++)
    {
        gyro_coeff[i] = calib_data.temp_coeff_gyro[i];
        accel_coeff[i] = calib_data.temp_coeff_accel[i];
    }
}

/***************************************
函数名: void Calibration_Enable_Auto_Save
说明: 启用/禁用自动保存
参数: uint8_t enable - 启用标志
返回: 无
***************************************/
void Calibration_Enable_Auto_Save(uint8_t enable)
{
    auto_save_enabled = enable;
}

/***************************************
函数名: void Calibration_Update
说明: 更新校准数据（定期调用）
参数: 无
返回: 无
***************************************/
void Calibration_Update(void)
{
    if(!auto_save_enabled || !data_changed) return;

    save_timer++;

    // 检查是否需要自动保存（假设5ms调用一次）
    if(save_timer >= (AUTO_SAVE_INTERVAL_MS / 5))
    {
        Calibration_Save();
        save_timer = 0;
    }
}

/***************************************
函数名: uint32_t Calibration_Get_CRC32
说明: 获取当前数据CRC32值
参数: 无
返回: uint32_t CRC32值
***************************************/
uint32_t Calibration_Get_CRC32(void)
{
    return Calculate_CRC32((uint8_t*)&calib_data,
                          sizeof(calibration_data_t) - sizeof(uint32_t));
}

/***************************************
函数名: uint8_t Calibration_Verify_Integrity
说明: 验证数据完整性
参数: 无
返回: uint8_t 验证结果(0-完整, 1-损坏)
***************************************/
uint8_t Calibration_Verify_Integrity(void)
{
    uint32_t calculated_crc = Calibration_Get_CRC32();
    return (calculated_crc == calib_data.crc32) ? 0 : 1;
}
