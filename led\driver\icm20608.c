#include "ti_msp_dl_config.h"
#include "system.h"
#include "ni2c.h"
#include "icm20608.h"
#include "filter.h"
#include "../apply/Fusion/FusionOffset.h"
#include <math.h>
#include <string.h>

// 全局变量定义
icm20608_data imu_data = {0};
static IMU_ID_READ IMU_ID = WHO_AM_I_ICM20608D;
static uint8_t imu_address = ICM20689_ADRESS;

// Butterworth滤波器参数和缓存
static lpf_param gyro_lpf_param[3];  // 陀螺仪X/Y/Z轴滤波参数
static lpf_buf gyro_lpf_buf[3];      // 陀螺仪X/Y/Z轴滤波缓存
static lpf_param accel_lpf_param[3]; // 加速度计X/Y/Z轴滤波参数
static lpf_buf accel_lpf_buf[3];     // 加速度计X/Y/Z轴滤波缓存

// 滤波器配置参数
#define SAMPLE_FREQ 200.0f    // 采样频率200Hz
#define GYRO_CUTOFF_FREQ 50.0f   // 陀螺仪截止频率50Hz
#define ACCEL_CUTOFF_FREQ 30.0f  // 加速度计截止频率30Hz

// 零偏校准配置参数
#define GYRO_CALIB_SAMPLES 100   // 零偏校准采样数量
#define GYRO_CALIB_DELAY_MS 10   // 校准采样间隔(ms)
#define GYRO_STABLE_THRESHOLD 0.5f // 静止状态阈值(°/s)

// 陀螺仪零偏校准变量
static vector3f gyro_offset = {0.0f, 0.0f, 0.0f};  // 陀螺仪零偏
static uint8_t gyro_calib_done = 0;                 // 校准完成标志
static uint8_t gyro_calib_in_progress = 0;          // 校准进行中标志

// FusionOffset算法变量
static FusionOffset fusion_offset;                  // FusionOffset算法结构
static uint8_t fusion_offset_init = 0;              // FusionOffset初始化标志
#define FUSION_SAMPLE_RATE 200                      // 采样频率200Hz

// 温度稳定性检测变量
static float temp_history[10];                      // 温度历史记录
static uint8_t temp_history_index = 0;              // 温度历史索引
static uint8_t temp_history_full = 0;               // 温度历史是否填满
static uint8_t temperature_stable_flag = 0;         // 温度稳定标志
static uint32_t temp_stable_timer = 0;              // 温度稳定计时器
#define TEMP_STABLE_THRESHOLD 0.5f                  // 温度稳定阈值(°C/min)
#define TEMP_STABLE_TIME_MS 5000                    // 温度稳定时间(ms)
#define TEMP_HISTORY_SIZE 10                        // 温度历史大小

// yaw值优化算法变量
static float yaw_history[20];                       // yaw值历史记录
static uint8_t yaw_history_index = 0;               // yaw值历史索引
static uint8_t yaw_history_full = 0;                // yaw值历史是否填满
static float yaw_drift_rate = 0.0f;                 // yaw值漂移率(°/s)
static uint32_t yaw_quality_score = 100;            // yaw值质量评分(0-100)
static float yaw_lpf_output = 0.0f;                 // yaw值低通滤波输出
static uint8_t yaw_reset_request = 0;               // yaw值重置请求
#define YAW_HISTORY_SIZE 20                         // yaw值历史大小
#define YAW_DRIFT_THRESHOLD 0.1f                    // yaw值漂移阈值(°/s)
#define YAW_LPF_ALPHA 0.95f                         // yaw值低通滤波系数

// 陀螺仪校准系数 (500dps量程: 65.5 LSB/dps)
#define GYRO_SCALE_FACTOR (1.0f / 65.5f)
// 加速度计校准系数 (4g量程: 8192 LSB/g)  
#define ACCEL_SCALE_FACTOR (1.0f / 8192.0f)

// 简化的姿态解算参数
static float dt = 0.005f;  // 5ms采样周期
static float alpha = 0.98f; // 互补滤波系数

/***************************************
函数名: ICM20608_Init
说明: ICM20608初始化
参数: 无
返回: uint8_t 错误标志(0-成功, 1-失败)
***************************************/
uint8_t ICM20608_Init(void)
{
    uint8_t fault = 0;
    
    // 复位ICM20608
    single_writei2c(imu_address, PWR_MGMT_1, 0x80);
    delay_ms(100);
    
    // 读取设备ID
    IMU_ID = (IMU_ID_READ)(single_readi2c(imu_address, WHO_AM_I));
    
    switch(IMU_ID)
    {
        case WHO_AM_I_ICM20608D:
        case WHO_AM_I_ICM20608G:
        case WHO_AM_I_ICM20602:
        {
            // 唤醒ICM20608
            single_writei2c(imu_address, PWR_MGMT_1, 0x01);
            delay_ms(10);
            
            // 配置采样率分频器
            single_writei2c(imu_address, 0x19, 0x00);   // 1kHz采样率
            
            // 配置陀螺仪低通滤波器
            single_writei2c(imu_address, 0x1A, 0x02);   // 陀螺仪低通滤波BW=92Hz
            
            // 配置陀螺仪量程 ±500dps
            single_writei2c(imu_address, 0x1B, 0x08);   
            
            // 配置加速度计量程 ±4g
            single_writei2c(imu_address, 0x1C, 0x08);   
            
            // 配置加速度计低通滤波器
            single_writei2c(imu_address, 0x1D, 0x03);   // 加速度计低通滤波BW=44.8Hz
            
            // 关闭FIFO和其他功能
            single_writei2c(imu_address, 0x6C, 0x00);   
            single_writei2c(imu_address, 0x1E, 0x00);   
            single_writei2c(imu_address, 0x23, 0x00);   
            
            delay_ms(200);
            
            // 验证配置
            uint8_t check_val = single_readi2c(imu_address, 0x1B);
            if(check_val != 0x08) fault = 1;
        }
        break;
        
        default:
            fault = 1;
            break;
    }
    
    if(fault == 0)
    {
        imu_data.init_ok = 1;
        // 初始化姿态角为0
        imu_data.angles.roll = 0.0f;
        imu_data.angles.pitch = 0.0f;
        imu_data.angles.yaw = 0.0f;

        // 初始化Butterworth滤波器
        for(int i = 0; i < 3; i++)
        {
            // 清零滤波器缓存
            memset(&gyro_lpf_buf[i], 0, sizeof(lpf_buf));
            memset(&accel_lpf_buf[i], 0, sizeof(lpf_buf));

            // 设置陀螺仪滤波器参数
            set_cutoff_frequency(SAMPLE_FREQ, GYRO_CUTOFF_FREQ, &gyro_lpf_param[i]);

            // 设置加速度计滤波器参数
            set_cutoff_frequency(SAMPLE_FREQ, ACCEL_CUTOFF_FREQ, &accel_lpf_param[i]);
        }

        // 初始化FusionOffset算法
        FusionOffsetInitialise(&fusion_offset, FUSION_SAMPLE_RATE);
        fusion_offset_init = 1;

        // 初始化校准数据管理
        Calibration_Init();

        // 从持久化存储加载校准数据
        if(Calibration_Get_Status() == CALIB_STATUS_VALID)
        {
            // 加载陀螺仪零偏
            gyro_offset = Calibration_Get_Gyro_Offset();
            gyro_calib_done = 1; // 标记校准完成
        }
    }
    
    return fault;
}

/***************************************
函数名: ICM20608_Calibrate_Gyro
说明: 陀螺仪零偏校准
参数: 无
返回: uint8_t 校准结果(0-成功, 1-失败)
***************************************/
uint8_t ICM20608_Calibrate_Gyro(void)
{
    if(!imu_data.init_ok) return 1;

    gyro_calib_in_progress = 1;
    gyro_calib_done = 0;

    vector3f gyro_sum = {0.0f, 0.0f, 0.0f};
    vector3f gyro_temp, accel_temp;
    float temp_val;
    uint16_t valid_samples = 0;

    // 采集校准样本
    for(uint16_t i = 0; i < GYRO_CALIB_SAMPLES; i++)
    {
        // 读取原始数据（不应用滤波器）
        uint8_t buf[14];
        int16_t temp;

        i2creadnbyte(imu_address, ACCEL_XOUT_H, buf, 14);

        // 解析陀螺仪数据
        gyro_temp.x = -(int16_t)((buf[8] << 8) | buf[9]) * GYRO_SCALE_FACTOR;
        gyro_temp.y =  (int16_t)((buf[10] << 8) | buf[11]) * GYRO_SCALE_FACTOR;
        gyro_temp.z = -(int16_t)((buf[12] << 8) | buf[13]) * GYRO_SCALE_FACTOR;

        // 检查是否静止（所有轴的角速度都小于阈值）
        if(fabsf(gyro_temp.x) < GYRO_STABLE_THRESHOLD &&
           fabsf(gyro_temp.y) < GYRO_STABLE_THRESHOLD &&
           fabsf(gyro_temp.z) < GYRO_STABLE_THRESHOLD)
        {
            gyro_sum.x += gyro_temp.x;
            gyro_sum.y += gyro_temp.y;
            gyro_sum.z += gyro_temp.z;
            valid_samples++;
        }

        delay_ms(GYRO_CALIB_DELAY_MS);
    }

    // 检查有效样本数量
    if(valid_samples < (GYRO_CALIB_SAMPLES * 0.8f)) // 至少80%的样本有效
    {
        gyro_calib_in_progress = 0;
        return 1; // 校准失败，设备可能在运动
    }

    // 计算平均零偏
    gyro_offset.x = gyro_sum.x / valid_samples;
    gyro_offset.y = gyro_sum.y / valid_samples;
    gyro_offset.z = gyro_sum.z / valid_samples;

    gyro_calib_done = 1;
    gyro_calib_in_progress = 0;

    // 保存校准数据到持久化存储
    Calibration_Set_Gyro_Offset(gyro_offset);
    Calibration_Save();

    return 0; // 校准成功
}

/***************************************
函数名: ICM20608_Get_Calib_Status
说明: 获取校准状态
参数: 无
返回: uint8_t 校准状态(0-未校准, 1-校准中, 2-校准完成)
***************************************/
uint8_t ICM20608_Get_Calib_Status(void)
{
    if(gyro_calib_in_progress) return 1;
    if(gyro_calib_done) return 2;
    return 0;
}

/***************************************
函数名: ICM20608_Get_Gyro_Offset
说明: 获取陀螺仪零偏值
参数: vector3f *offset - 零偏值指针
返回: 无
***************************************/
void ICM20608_Get_Gyro_Offset(vector3f *offset)
{
    if(fusion_offset_init)
    {
        // 返回FusionOffset算法的零偏值
        offset->x = fusion_offset.gyroscopeOffset.axis.x;
        offset->y = fusion_offset.gyroscopeOffset.axis.y;
        offset->z = fusion_offset.gyroscopeOffset.axis.z;
    }
    else
    {
        // 返回简单校准的零偏值
        *offset = gyro_offset;
    }
}

/***************************************
函数名: ICM20608_Get_Fusion_Status
说明: 获取FusionOffset算法状态
参数: 无
返回: uint8_t FusionOffset状态(0-未初始化, 1-已初始化)
***************************************/
uint8_t ICM20608_Get_Fusion_Status(void)
{
    return fusion_offset_init;
}

/***************************************
函数名: ICM20608_Get_Fusion_Timer
说明: 获取FusionOffset算法计时器值
参数: 无
返回: uint32_t 计时器值(用于判断静止状态持续时间)
***************************************/
uint32_t ICM20608_Get_Fusion_Timer(void)
{
    if(fusion_offset_init)
    {
        return fusion_offset.timer;
    }
    return 0;
}

/***************************************
函数名: ICM20608_Update_Temperature_Stability
说明: 更新温度稳定性检测
参数: float current_temp - 当前温度
返回: 无
***************************************/
static void ICM20608_Update_Temperature_Stability(float current_temp)
{
    // 添加当前温度到历史记录
    temp_history[temp_history_index] = current_temp;
    temp_history_index = (temp_history_index + 1) % TEMP_HISTORY_SIZE;

    if(!temp_history_full && temp_history_index == 0)
    {
        temp_history_full = 1;
    }

    // 如果历史记录未满，认为温度不稳定
    if(!temp_history_full)
    {
        temperature_stable_flag = 0;
        temp_stable_timer = 0;
        return;
    }

    // 计算温度变化率
    float temp_min = temp_history[0];
    float temp_max = temp_history[0];

    for(int i = 1; i < TEMP_HISTORY_SIZE; i++)
    {
        if(temp_history[i] < temp_min) temp_min = temp_history[i];
        if(temp_history[i] > temp_max) temp_max = temp_history[i];
    }

    float temp_range = temp_max - temp_min;

    // 判断温度是否稳定（温度变化范围小于阈值）
    if(temp_range <= TEMP_STABLE_THRESHOLD)
    {
        temp_stable_timer++;
        // 温度稳定时间超过阈值，设置稳定标志
        if(temp_stable_timer >= (TEMP_STABLE_TIME_MS / 5)) // 5ms采样间隔
        {
            temperature_stable_flag = 1;
        }
    }
    else
    {
        // 温度变化超过阈值，重置稳定状态
        temperature_stable_flag = 0;
        temp_stable_timer = 0;
    }
}

/***************************************
函数名: ICM20608_Is_Temperature_Stable
说明: 获取温度稳定状态
参数: 无
返回: uint8_t 温度稳定状态(0-不稳定, 1-稳定)
***************************************/
uint8_t ICM20608_Is_Temperature_Stable(void)
{
    return temperature_stable_flag;
}

/***************************************
函数名: ICM20608_Get_Temperature_Range
说明: 获取温度变化范围
参数: 无
返回: float 温度变化范围(°C)
***************************************/
float ICM20608_Get_Temperature_Range(void)
{
    if(!temp_history_full) return 999.0f; // 历史记录未满

    float temp_min = temp_history[0];
    float temp_max = temp_history[0];

    for(int i = 1; i < TEMP_HISTORY_SIZE; i++)
    {
        if(temp_history[i] < temp_min) temp_min = temp_history[i];
        if(temp_history[i] > temp_max) temp_max = temp_history[i];
    }

    return temp_max - temp_min;
}

/***************************************
函数名: ICM20608_Update_Yaw_Quality
说明: 更新yaw值质量评估
参数: float current_yaw - 当前yaw值
返回: 无
***************************************/
static void ICM20608_Update_Yaw_Quality(float current_yaw)
{
    // 添加当前yaw值到历史记录
    yaw_history[yaw_history_index] = current_yaw;
    yaw_history_index = (yaw_history_index + 1) % YAW_HISTORY_SIZE;

    if(!yaw_history_full && yaw_history_index == 0)
    {
        yaw_history_full = 1;
    }

    // 如果历史记录未满，质量评分较低
    if(!yaw_history_full)
    {
        yaw_quality_score = 50;
        yaw_drift_rate = 0.0f;
        return;
    }

    // 计算yaw值漂移率（线性回归斜率）
    float sum_x = 0.0f, sum_y = 0.0f, sum_xy = 0.0f, sum_x2 = 0.0f;

    for(int i = 0; i < YAW_HISTORY_SIZE; i++)
    {
        float x = (float)i;
        float y = yaw_history[i];

        sum_x += x;
        sum_y += y;
        sum_xy += x * y;
        sum_x2 += x * x;
    }

    float n = (float)YAW_HISTORY_SIZE;
    yaw_drift_rate = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x);

    // 转换为°/s（历史记录间隔约0.005s）
    yaw_drift_rate *= 200.0f; // 200Hz采样率

    // 计算质量评分
    float abs_drift = fabsf(yaw_drift_rate);
    if(abs_drift < YAW_DRIFT_THRESHOLD)
    {
        yaw_quality_score = 100 - (uint32_t)(abs_drift * 500); // 漂移越小质量越高
    }
    else
    {
        yaw_quality_score = 50 - (uint32_t)((abs_drift - YAW_DRIFT_THRESHOLD) * 100);
        if(yaw_quality_score > 50) yaw_quality_score = 0; // 防止下溢
    }

    // 限制质量评分范围
    if(yaw_quality_score > 100) yaw_quality_score = 100;
}

/***************************************
函数名: ICM20608_Get_Yaw_Quality
说明: 获取yaw值质量评分
参数: 无
返回: uint32_t yaw值质量评分(0-100)
***************************************/
uint32_t ICM20608_Get_Yaw_Quality(void)
{
    return yaw_quality_score;
}

/***************************************
函数名: ICM20608_Get_Yaw_Drift_Rate
说明: 获取yaw值漂移率
参数: 无
返回: float yaw值漂移率(°/s)
***************************************/
float ICM20608_Get_Yaw_Drift_Rate(void)
{
    return yaw_drift_rate;
}

/***************************************
函数名: ICM20608_Reset_Yaw
说明: 重置yaw值
参数: 无
返回: 无
***************************************/
void ICM20608_Reset_Yaw(void)
{
    yaw_reset_request = 1;
}

/***************************************
函数名: uint8_t ICM20608_Save_Calibration
说明: 保存校准数据
参数: 无
返回: uint8_t 保存结果(0-成功, 1-失败)
***************************************/
uint8_t ICM20608_Save_Calibration(void)
{
    // 更新当前校准数据
    if(gyro_calib_done)
    {
        Calibration_Set_Gyro_Offset(gyro_offset);
    }

    // 保存到持久化存储
    return Calibration_Save();
}

/***************************************
函数名: uint8_t ICM20608_Load_Calibration
说明: 加载校准数据
参数: 无
返回: uint8_t 加载结果(0-成功, 1-失败)
***************************************/
uint8_t ICM20608_Load_Calibration(void)
{
    uint8_t result = Calibration_Load();

    if(result == 0 && Calibration_Get_Status() == CALIB_STATUS_VALID)
    {
        // 加载陀螺仪零偏
        gyro_offset = Calibration_Get_Gyro_Offset();
        gyro_calib_done = 1;
    }

    return result;
}

/***************************************
函数名: uint8_t ICM20608_Reset_Calibration
说明: 重置校准数据
参数: 无
返回: uint8_t 重置结果(0-成功, 1-失败)
***************************************/
uint8_t ICM20608_Reset_Calibration(void)
{
    // 重置本地校准数据
    gyro_offset.x = 0.0f;
    gyro_offset.y = 0.0f;
    gyro_offset.z = 0.0f;
    gyro_calib_done = 0;

    // 重置持久化校准数据
    return Calibration_Reset();
}

/***************************************
函数名: calib_status_t ICM20608_Get_Calibration_Status
说明: 获取校准数据状态
参数: 无
返回: calib_status_t 校准状态
***************************************/
calib_status_t ICM20608_Get_Calibration_Status(void)
{
    return Calibration_Get_Status();
}

/***************************************
函数名: ICM20608_Read_Data
说明: 读取陀螺仪/加速度计/温度数据
参数: vector3f *gyro - 陀螺仪数据指针
      vector3f *accel - 加速度计数据指针  
      float *temperature - 温度数据指针
返回: 无
***************************************/
void ICM20608_Read_Data(vector3f *gyro, vector3f *accel, float *temperature)
{
    uint8_t buf[14];
    int16_t temp;
    
    // 连续读取14字节数据
    i2creadnbyte(imu_address, ACCEL_XOUT_H, buf, 14);
    
    // 解析加速度计数据
    accel->x = -(int16_t)((buf[0] << 8) | buf[1]);
    accel->y =  (int16_t)((buf[2] << 8) | buf[3]);
    accel->z = -(int16_t)((buf[4] << 8) | buf[5]);
    
    // 解析温度数据
    temp = (int16_t)((buf[6] << 8) | buf[7]);
    *temperature = 25.0f + (float)((temp - 25.0f) / 326.8f);
    
    // 解析陀螺仪数据
    gyro->x = -(int16_t)((buf[8] << 8) | buf[9]);
    gyro->y =  (int16_t)((buf[10] << 8) | buf[11]);
    gyro->z = -(int16_t)((buf[12] << 8) | buf[13]);
    
    // 转换为物理单位
    gyro->x *= GYRO_SCALE_FACTOR;
    gyro->y *= GYRO_SCALE_FACTOR;
    gyro->z *= GYRO_SCALE_FACTOR;

    accel->x *= ACCEL_SCALE_FACTOR;
    accel->y *= ACCEL_SCALE_FACTOR;
    accel->z *= ACCEL_SCALE_FACTOR;

    // 保存原始数据
    imu_data.gyro_raw = *gyro;
    imu_data.accel_raw = *accel;

    // 应用Butterworth滤波器
    if(imu_data.init_ok)
    {
        // 陀螺仪数据滤波
        gyro->x = LPButterworth(gyro->x, &gyro_lpf_buf[0], &gyro_lpf_param[0]);
        gyro->y = LPButterworth(gyro->y, &gyro_lpf_buf[1], &gyro_lpf_param[1]);
        gyro->z = LPButterworth(gyro->z, &gyro_lpf_buf[2], &gyro_lpf_param[2]);

        // 应用FusionOffset算法进行高级零偏校正
        if(fusion_offset_init)
        {
            FusionVector gyro_fusion = {
                .axis.x = gyro->x,
                .axis.y = gyro->y,
                .axis.z = gyro->z
            };

            gyro_fusion = FusionOffsetUpdate(&fusion_offset, gyro_fusion);

            gyro->x = gyro_fusion.axis.x;
            gyro->y = gyro_fusion.axis.y;
            gyro->z = gyro_fusion.axis.z;
        }
        else
        {
            // 如果FusionOffset未初始化，使用简单零偏校正
            if(gyro_calib_done)
            {
                gyro->x -= gyro_offset.x;
                gyro->y -= gyro_offset.y;
                gyro->z -= gyro_offset.z;
            }
        }

        // 加速度计数据滤波
        accel->x = LPButterworth(accel->x, &accel_lpf_buf[0], &accel_lpf_param[0]);
        accel->y = LPButterworth(accel->y, &accel_lpf_buf[1], &accel_lpf_param[1]);
        accel->z = LPButterworth(accel->z, &accel_lpf_buf[2], &accel_lpf_param[2]);
    }

    // 保存滤波后的数据
    imu_data.gyro_dps = *gyro;
    imu_data.accel_g = *accel;
    imu_data.temperature = *temperature;

    // 更新温度稳定性检测
    ICM20608_Update_Temperature_Stability(*temperature);

    // 更新校准数据管理（自动保存）
    Calibration_Update();
}

/***************************************
函数名: ICM20608_Update_Angles
说明: 更新姿态角(简化版互补滤波)
参数: 无
返回: 无
***************************************/
void ICM20608_Update_Angles(void)
{
    if(!imu_data.init_ok) return;

    // 根据温度稳定性调整算法精度
    float current_alpha = alpha;
    if(!temperature_stable_flag)
    {
        // 温度不稳定时，增加陀螺仪权重，减少积分漂移
        current_alpha = 0.99f; // 更保守的融合系数
    }

    // 从加速度计计算倾斜角
    float accel_roll = atan2f(imu_data.accel_g.y,
                             sqrtf(imu_data.accel_g.x * imu_data.accel_g.x +
                                   imu_data.accel_g.z * imu_data.accel_g.z)) * 57.2958f;

    float accel_pitch = atan2f(-imu_data.accel_g.x,
                              sqrtf(imu_data.accel_g.y * imu_data.accel_g.y +
                                    imu_data.accel_g.z * imu_data.accel_g.z)) * 57.2958f;

    // 陀螺仪积分
    imu_data.angles.roll += imu_data.gyro_dps.x * dt;
    imu_data.angles.pitch += imu_data.gyro_dps.y * dt;

    // 优化的yaw值积分算法
    float yaw_gain = temperature_stable_flag ? 1.0f : 0.8f;

    // 自适应零偏校正
    if(fabsf(imu_data.gyro_dps.z) < 0.1f && yaw_history_full)
    {
        // 静止状态下进行零偏自适应调整
        yaw_gain *= 0.5f; // 减少静止时的积分增益
    }

    // 处理yaw值重置请求
    if(yaw_reset_request)
    {
        imu_data.angles.yaw = 0.0f;
        yaw_lpf_output = 0.0f;
        // 清空yaw历史记录
        for(int i = 0; i < YAW_HISTORY_SIZE; i++)
        {
            yaw_history[i] = 0.0f;
        }
        yaw_history_index = 0;
        yaw_history_full = 0;
        yaw_reset_request = 0;
    }
    else
    {
        // 改进的yaw值积分（使用梯形积分法）
        static float prev_gyro_z = 0.0f;
        float yaw_increment = (imu_data.gyro_dps.z + prev_gyro_z) * 0.5f * dt * yaw_gain;
        imu_data.angles.yaw += yaw_increment;
        prev_gyro_z = imu_data.gyro_dps.z;

        // yaw值低通滤波
        yaw_lpf_output = YAW_LPF_ALPHA * yaw_lpf_output + (1.0f - YAW_LPF_ALPHA) * imu_data.angles.yaw;

        // 平滑的角度范围限制
        while(imu_data.angles.yaw > 180.0f) imu_data.angles.yaw -= 360.0f;
        while(imu_data.angles.yaw < -180.0f) imu_data.angles.yaw += 360.0f;

        // 更新yaw值质量评估
        ICM20608_Update_Yaw_Quality(imu_data.angles.yaw);
    }

    // 互补滤波融合
    imu_data.angles.roll = current_alpha * imu_data.angles.roll + (1.0f - current_alpha) * accel_roll;
    imu_data.angles.pitch = current_alpha * imu_data.angles.pitch + (1.0f - current_alpha) * accel_pitch;
}

/***************************************
函数名: ICM20608_Get_Angles
说明: 获取当前姿态角
参数: euler_angles *angles - 姿态角结构体指针
返回: 无
***************************************/
void ICM20608_Get_Angles(euler_angles *angles)
{
    *angles = imu_data.angles;
}
