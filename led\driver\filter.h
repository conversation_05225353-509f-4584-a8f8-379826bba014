#ifndef __FILTER_H
#define __FILTER_H

#include <stdint.h>
#include <math.h>

// 低通滤波器缓冲区结构体
typedef struct
{
    float input[3];   // 输入历史数据
    float output[3];  // 输出历史数据
} lpf_buf;

// 低通滤波器参数结构体
typedef struct
{
    float a[3];  // 反馈系数
    float b[3];  // 前馈系数
} lpf_param;

// 函数声明
float LPButterworth(float curr_input, lpf_buf *buf, lpf_param *params);
void set_cutoff_frequency(float sample_frequent, float cutoff_frequent, lpf_param *LPF);

#endif
