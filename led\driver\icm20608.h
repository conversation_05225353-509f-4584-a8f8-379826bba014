#ifndef __ICM20608_H
#define __ICM20608_H

#include "datatype.h"
#include "calibration.h"

#define IMU_ICM20689_ID  0x98

// ICM20608 寄存器定义
#define	SMPLRT_DIV		0x19
#define	MPU_CONFIG		0x1A
#define	GYRO_CONFIG		0x1B
#define	ACCEL_CONFIG  0x1C
#define ACCEL_CONFIG2 0x1D
#define	ACCEL_XOUT_H	0x3B
#define	ACCEL_XOUT_L	0x3C
#define	ACCEL_YOUT_H	0x3D
#define	ACCEL_YOUT_L	0x3E
#define	ACCEL_ZOUT_H	0x3F
#define	ACCEL_ZOUT_L	0x40
#define	TEMP_OUT_H		0x41
#define	TEMP_OUT_L		0x42
#define	GYRO_XOUT_H		0x43
#define	GYRO_XOUT_L		0x44
#define	GYRO_YOUT_H		0x45
#define	GYRO_YOUT_L		0x46
#define	GYRO_ZOUT_H		0x47
#define	GYRO_ZOUT_L		0x48
#define	PWR_MGMT_1		0x6B
#define	WHO_AM_I		  0x75
#define USER_CTRL		  0x6A
#define INT_PIN_CFG		0x37

#define ICM20689_ADRESS		0x68

// 使用datatype.h中的定义，无需重复定义

// 姿态角结构体定义
typedef struct
{
    float roll;   // 翻转角
    float pitch;  // 俯仰角
    float yaw;    // 偏航角
}euler_angles;

// ICM20608传感器数据结构体
typedef struct
{
    vector3f gyro_raw;      // 原始陀螺仪数据
    vector3f accel_raw;     // 原始加速度计数据
    vector3f gyro_dps;      // 校准后陀螺仪数据(度/秒)
    vector3f accel_g;       // 校准后加速度计数据(g)
    float temperature;      // 温度
    euler_angles angles;    // 姿态角
    uint8_t init_ok;        // 初始化完成标志
}icm20608_data;

// 函数声明
uint8_t ICM20608_Init(void);
void ICM20608_Read_Data(vector3f *gyro, vector3f *accel, float *temperature);
void ICM20608_Update_Angles(void);
void ICM20608_Get_Angles(euler_angles *angles);

// 零偏校准相关函数
uint8_t ICM20608_Calibrate_Gyro(void);      // 陀螺仪零偏校准
uint8_t ICM20608_Get_Calib_Status(void);    // 获取校准状态
void ICM20608_Get_Gyro_Offset(vector3f *offset); // 获取陀螺仪零偏值

// FusionOffset算法相关函数
uint8_t ICM20608_Get_Fusion_Status(void);   // 获取FusionOffset状态
uint32_t ICM20608_Get_Fusion_Timer(void);   // 获取FusionOffset计时器值

// 温度稳定性检测相关函数
uint8_t ICM20608_Is_Temperature_Stable(void); // 获取温度稳定状态
float ICM20608_Get_Temperature_Range(void);   // 获取温度变化范围

// yaw值优化相关函数
uint32_t ICM20608_Get_Yaw_Quality(void);      // 获取yaw值质量评分
float ICM20608_Get_Yaw_Drift_Rate(void);      // 获取yaw值漂移率
void ICM20608_Reset_Yaw(void);                // 重置yaw值

// 校准数据持久化相关函数
uint8_t ICM20608_Save_Calibration(void);      // 保存校准数据
uint8_t ICM20608_Load_Calibration(void);      // 加载校准数据
uint8_t ICM20608_Reset_Calibration(void);     // 重置校准数据
calib_status_t ICM20608_Get_Calibration_Status(void); // 获取校准数据状态

// 全局变量声明
extern icm20608_data imu_data;

#endif
