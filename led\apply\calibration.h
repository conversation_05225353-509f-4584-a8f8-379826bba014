#ifndef __CALIBRATION_H
#define __CALIBRATION_H

#include <stdint.h>
#include "datatype.h"

// 校准数据版本号
#define CALIB_DATA_VERSION 0x01

// 校准数据魔数（用于验证数据有效性）
#define CALIB_MAGIC_NUMBER 0x12345678

// 校准数据结构
typedef struct {
    uint32_t magic_number;          // 魔数，用于验证数据有效性
    uint8_t version;                // 数据版本号
    uint8_t reserved[3];            // 保留字节，用于对齐
    
    // 陀螺仪校准数据
    vector3f gyro_offset;           // 陀螺仪零偏(°/s)
    vector3f gyro_scale;            // 陀螺仪比例因子
    
    // 加速度计校准数据
    vector3f accel_offset;          // 加速度计零偏(g)
    vector3f accel_scale;           // 加速度计比例因子
    
    // 温度补偿参数
    float temp_coeff_gyro[3];       // 陀螺仪温度系数(°/s/°C)
    float temp_coeff_accel[3];      // 加速度计温度系数(g/°C)
    float reference_temp;           // 参考温度(°C)
    
    // 系统参数
    uint32_t calib_timestamp;       // 校准时间戳
    uint32_t usage_count;           // 使用次数
    float yaw_drift_compensation;   // yaw值漂移补偿(°/s)
    
    // 数据完整性校验
    uint32_t crc32;                 // CRC32校验值
} calibration_data_t;

// 校准状态枚举
typedef enum {
    CALIB_STATUS_NONE = 0,          // 无校准数据
    CALIB_STATUS_LOADING,           // 正在加载
    CALIB_STATUS_VALID,             // 校准数据有效
    CALIB_STATUS_INVALID,           // 校准数据无效
    CALIB_STATUS_SAVING,            // 正在保存
    CALIB_STATUS_ERROR              // 错误状态
} calib_status_t;

// 函数声明
void Calibration_Init(void);                           // 初始化校准数据管理
uint8_t Calibration_Load(void);                        // 加载校准数据
uint8_t Calibration_Save(void);                        // 保存校准数据
uint8_t Calibration_Reset(void);                       // 重置校准数据
calib_status_t Calibration_Get_Status(void);           // 获取校准状态

// 数据访问接口
void Calibration_Set_Gyro_Offset(vector3f offset);     // 设置陀螺仪零偏
vector3f Calibration_Get_Gyro_Offset(void);            // 获取陀螺仪零偏
void Calibration_Set_Accel_Offset(vector3f offset);    // 设置加速度计零偏
vector3f Calibration_Get_Accel_Offset(void);           // 获取加速度计零偏
void Calibration_Set_Temp_Coeff(float gyro_coeff[3], float accel_coeff[3]); // 设置温度系数
void Calibration_Get_Temp_Coeff(float gyro_coeff[3], float accel_coeff[3]); // 获取温度系数

// 自动保存控制
void Calibration_Enable_Auto_Save(uint8_t enable);     // 启用/禁用自动保存
void Calibration_Update(void);                         // 更新校准数据（定期调用）
uint32_t Calibration_Get_CRC32(void);                  // 获取当前数据CRC32值
uint8_t Calibration_Verify_Integrity(void);            // 验证数据完整性

#endif
