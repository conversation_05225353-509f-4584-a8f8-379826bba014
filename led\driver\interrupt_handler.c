#include "ti_msp_dl_config.h"
#include "encoder_driver.h"

void GROUP1_IRQHandler(void)
{
    if(DL_Interrupt_getStatusGroup(DL_INTERRUPT_GROUP_1, DL_INTERRUPT_GROUP1_GPIOB))
    {
        // 修复：交换左右轮中断处理函数调用，解决左右轮测速测反问题
        if(DL_GPIO_getEnabledInterruptStatus(PORTB_PORT, PORTB_RIGHT_PULSE_PIN))
        {
            QEI1_IRQHandler();  // 右轮脉冲触发左轮处理函数
            DL_GPIO_clearInterruptStatus(PORTB_PORT, PORTB_RIGHT_PULSE_PIN);
        }

        if(DL_GPIO_getEnabledInterruptStatus(PORTB_PORT, PORTB_LEFT_PULSE_PIN))
        {
            QEI0_IRQHandler();  // 左轮脉冲触发右轮处理函数
            DL_GPIO_clearInterruptStatus(PORTB_PORT, PORTB_LEFT_PULSE_PIN);
        }
        DL_Interrupt_clearGroup(DL_INTERRUPT_GROUP_1, DL_INTERRUPT_GROUP1_GPIOB);
    }
}
