#include "headfile.h"
#include "filter.h"
#include <math.h>

// 定义数学常数
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

/***************************************************
函数名: float LPButterworth(float curr_input, lpf_buf *buf, lpf_param *params)
说明:   二阶Butterworth低通滤波器
入口:   float curr_input - 当前滤波器输入
        lpf_buf *buf - 滤波器中间状态
        lpf_param *params - 滤波器参数
出口:   float 滤波器输出值
备注:   无
作者:   无名创新
****************************************************/
float LPButterworth(float curr_input, lpf_buf *buf, lpf_param *params)
{
    // 初始化检查：如果所有历史数据都为0，则初始化为当前输入值
    if(buf->output[0] == 0 && buf->output[1] == 0 && buf->output[2] == 0 &&
       buf->input[0] == 0 && buf->input[1] == 0 && buf->input[2] == 0)
    {
        buf->output[0] = curr_input;
        buf->output[1] = curr_input;
        buf->output[2] = curr_input;
        buf->input[0] = curr_input;
        buf->input[1] = curr_input;
        buf->input[2] = curr_input;
        return curr_input;
    }
    
    // 更新输入历史数据 x(n)
    buf->input[2] = curr_input;
    
    // Butterworth滤波器差分方程：
    // y(n) = b0*x(n) + b1*x(n-1) + b2*x(n-2) - a1*y(n-1) - a2*y(n-2)
    buf->output[2] = params->b[0] * buf->input[2]
                   + params->b[1] * buf->input[1]
                   + params->b[2] * buf->input[0]
                   - params->a[1] * buf->output[1]
                   - params->a[2] * buf->output[0];
    
    // 更新输入历史数据 x(n) -> x(n-1) -> x(n-2)
    buf->input[0] = buf->input[1];
    buf->input[1] = buf->input[2];
    
    // 更新输出历史数据 y(n) -> y(n-1) -> y(n-2)
    buf->output[0] = buf->output[1];
    buf->output[1] = buf->output[2];
    
    // NaN检测和保护
    for(uint16_t i = 0; i < 3; i++)
    {
        if(isnan(buf->output[i]) == 1 || isnan(buf->input[i]) == 1)
        {
            // 如果检测到NaN，重新初始化滤波器
            buf->output[0] = curr_input;
            buf->output[1] = curr_input;
            buf->output[2] = curr_input;
            buf->input[0] = curr_input;
            buf->input[1] = curr_input;
            buf->input[2] = curr_input;
            return curr_input;
        }
    }
    
    return buf->output[2];
}

/***************************************************
函数名: void set_cutoff_frequency(float sample_frequent, float cutoff_frequent, lpf_param *LPF)
说明:   设置Butterworth低通滤波器截止频率
入口:   float sample_frequent - 采样频率
        float cutoff_frequent - 截止频率
        lpf_param *LPF - 滤波器参数
出口:   无
备注:   使用双线性变换设计二阶Butterworth滤波器
作者:   无名创新
****************************************************/
void set_cutoff_frequency(float sample_frequent, float cutoff_frequent, lpf_param *LPF)
{
    // 检查截止频率有效性
    if (cutoff_frequent <= 0.0f) {
        // 无滤波，直通
        return;
    }
    
    // 计算预扭曲频率比
    float fr = sample_frequent / cutoff_frequent;
    float ohm = tanf(3.14159265358979323846f / fr); // 预扭曲频率
    float c = 1.0f + 2.0f * cosf(3.14159265358979323846f / 4.0f) * ohm + ohm * ohm; // 归一化因子
    
    // 计算滤波器系数
    // 分子系数 (b0, b1, b2)
    LPF->b[0] = ohm * ohm / c;
    LPF->b[1] = 2.0f * LPF->b[0];
    LPF->b[2] = LPF->b[0];
    
    // 分母系数 (a0, a1, a2)
    LPF->a[0] = 1.0f; // a0 = 1 (归一化)
    LPF->a[1] = 2.0f * (ohm * ohm - 1.0f) / c;
    LPF->a[2] = (1.0f - 2.0f * cosf(3.14159265358979323846f / 4.0f) * ohm + ohm * ohm) / c;
}
