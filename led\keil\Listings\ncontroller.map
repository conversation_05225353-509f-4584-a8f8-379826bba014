Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKey) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOut) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorData) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration) refers to dl_aes.o(.text.DL_AES_saveConfiguration) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration) refers to dl_aes.o(.text.DL_AES_restoreConfiguration) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_calculateBlock32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_calculateBlock16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange16) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_init) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.ramfunc) refers to dl_flashctl.o(.ramfunc) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for DL_FlashCTL_massErase
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryReset) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for DL_FlashCTL_massEraseFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for DL_FlashCTL_massEraseMultiBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_protectSector) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerify) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_configOperation) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_increaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_decreaseGain) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration) refers to dl_trng.o(.text.DL_TRNG_saveConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_configReference) refers to dl_vref.o(.text.DL_VREF_configReference) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig) refers to dl_vref.o(.text.DL_VREF_setClockConfig) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig) refers to dl_vref.o(.text.DL_VREF_getClockConfig) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to startup_mspm0g350x_uvision.o(.text) for Default_Handler
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to system.o(.text.NMI_Handler) for NMI_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to system.o(.text.HardFault_Handler) for HardFault_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to system.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to interrupt_handler.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to ntimer.o(.text.TIMG8_IRQHandler) for TIMG8_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to nuart.o(.text.UART3_IRQHandler) for UART3_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to nuart.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to nuart.o(.text.UART2_IRQHandler) for UART2_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to nuart.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to ntimer.o(.text.TIMG0_IRQHandler) for TIMG0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to ntimer.o(.text.TIMG6_IRQHandler) for TIMG6_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to ntimer.o(.text.TIMG12_IRQHandler) for TIMG12_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    main.o(.text.main) refers to nuart.o(.text.usart_irq_config) for usart_irq_config
    main.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    main.o(.text.main) refers to system.o(.text.ncontroller_set_priority) for ncontroller_set_priority
    main.o(.text.main) refers to oled.o(.text.OLED_Init) for OLED_Init
    main.o(.text.main) refers to led_app.o(.text.LED_App_Init) for LED_App_Init
    main.o(.text.main) refers to nbutton.o(.text.Button_Init) for Button_Init
    main.o(.text.main) refers to buzzer_app.o(.text.Buzzer_Init) for Buzzer_Init
    main.o(.text.main) refers to uart_app.o(.text.UART0_App_Init) for UART0_App_Init
    main.o(.text.main) refers to motor_app.o(.text.Motor_App_Init) for Motor_App_Init
    main.o(.text.main) refers to encoder_driver.o(.text.Encoder_Init) for Encoder_Init
    main.o(.text.main) refers to ntimer.o(.text.timer_irq_config) for timer_irq_config
    main.o(.text.main) refers to encoder_driver.o(.text.get_wheel_speed) for get_wheel_speed
    main.o(.text.main) refers to ui.o(.text.screen_display) for screen_display
    main.o(.text.main) refers to uart_process.o(.text.uart_process) for uart_process
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init) for SYSCFG_DL_PWM_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init) for SYSCFG_DL_PWM_2_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init) for SYSCFG_DL_TIMER_G0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init) for SYSCFG_DL_TIMER_G6_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init) for SYSCFG_DL_TIMER_G8_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init) for SYSCFG_DL_TIMER_G12_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for SYSCFG_DL_UART_2_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) for SYSCFG_DL_UART_3_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init) for SYSCFG_DL_SPI_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) for SYSCFG_DL_ADC12_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for SYSCFG_DL_DMA_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for SYSCFG_DL_SYSCTL_CLK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_1Backup) for gPWM_1Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_2Backup) for gPWM_2Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gTIMER_G6Backup) for gTIMER_G6Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gUART_3Backup) for gUART_3Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gSPI_0Backup) for gSPI_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init) refers to ti_msp_dl_config.o(.rodata.gPWM_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init) refers to ti_msp_dl_config.o(.rodata.gPWM_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init) refers to ti_msp_dl_config.o(.rodata.gPWM_2ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init) refers to ti_msp_dl_config.o(.rodata.gPWM_2Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_2_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_G0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_G0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_G0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_G6ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_G6TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_G6_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_G8ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_G8TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_G8_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_G12ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_G12TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_G12_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.rodata.gUART_3ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.rodata.gUART_3Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for DL_SPI_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init) refers to dl_spi.o(.text.DL_SPI_init) for DL_SPI_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init) refers to ti_msp_dl_config.o(.rodata.gSPI_0_clockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init) refers to ti_msp_dl_config.o(.rodata.gSPI_0_config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) refers to ti_msp_dl_config.o(.rodata.gADC12_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for SYSCFG_DL_DMA_CH0_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for DL_UART_Main_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for DL_SPI_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_1Backup) for gPWM_1Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_2Backup) for gPWM_2Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_G6Backup) for gTIMER_G6Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gUART_3Backup) for gUART_3Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_0Backup) for gSPI_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for DL_UART_Main_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for DL_SPI_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_1Backup) for gPWM_1Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_2Backup) for gPWM_2Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_G6Backup) for gTIMER_G6Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gUART_3Backup) for gUART_3Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_0Backup) for gSPI_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.rodata.gDMA_CH0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_App_Init) refers to led_app.o(.text.LED_App_Init) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.RGB_All_Off) refers to led_app.o(.text.RGB_All_Off) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Red_Control) refers to led_app.o(.text.LED_Red_Control) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Green_Control) refers to led_app.o(.text.LED_Green_Control) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Blue_Control) refers to led_app.o(.text.LED_Blue_Control) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Red_On) refers to led_app.o(.text.LED_Red_On) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Red_Off) refers to led_app.o(.text.LED_Red_Off) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Green_On) refers to led_app.o(.text.LED_Green_On) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Green_Off) refers to led_app.o(.text.LED_Green_Off) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Blue_On) refers to led_app.o(.text.LED_Blue_On) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Blue_Off) refers to led_app.o(.text.LED_Blue_Off) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Red_Toggle) refers to led_app.o(.text.LED_Red_Toggle) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Green_Toggle) refers to led_app.o(.text.LED_Green_Toggle) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Blue_Toggle) refers to led_app.o(.text.LED_Blue_Toggle) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.RGB_Set_Color) refers to led_app.o(.text.RGB_Set_Color) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.RGB_All_On) refers to led_app.o(.text.RGB_All_On) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Red_Get_State) refers to led_app.o(.text.LED_Red_Get_State) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Green_Get_State) refers to led_app.o(.text.LED_Green_Get_State) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.LED_Blue_Get_State) refers to led_app.o(.text.LED_Blue_Get_State) for [Anonymous Symbol]
    led_app.o(.ARM.exidx.text.RGB_Get_Color) refers to led_app.o(.text.RGB_Get_Color) for [Anonymous Symbol]
    led_app.o(.text.RGB_Test_Sequence) refers to system.o(.text.Delay_Ms) for Delay_Ms
    led_app.o(.ARM.exidx.text.RGB_Test_Sequence) refers to led_app.o(.text.RGB_Test_Sequence) for [Anonymous Symbol]
    led_app.o(.text.RGB_Breathing_Effect) refers to system.o(.text.Delay_Ms) for Delay_Ms
    led_app.o(.ARM.exidx.text.RGB_Breathing_Effect) refers to led_app.o(.text.RGB_Breathing_Effect) for [Anonymous Symbol]
    nbutton.o(.text.Button_Init) refers to nbutton.o(.bss._button) for _button
    nbutton.o(.ARM.exidx.text.Button_Init) refers to nbutton.o(.text.Button_Init) for [Anonymous Symbol]
    nbutton.o(.text.Read_Button_State_One) refers to system.o(.text.millis) for millis
    nbutton.o(.text.Read_Button_State_One) refers to buzzer_app.o(.text.Buzzer_Beep) for Buzzer_Beep
    nbutton.o(.ARM.exidx.text.Read_Button_State_One) refers to nbutton.o(.text.Read_Button_State_One) for [Anonymous Symbol]
    nbutton.o(.text.read_button_state_all) refers to nbutton.o(.text.Read_Button_State_One) for Read_Button_State_One
    nbutton.o(.text.read_button_state_all) refers to nbutton.o(.bss._button) for _button
    nbutton.o(.ARM.exidx.text.read_button_state_all) refers to nbutton.o(.text.read_button_state_all) for [Anonymous Symbol]
    nbutton.o(.text.get_key_val) refers to nbutton.o(.bss._button) for _button
    nbutton.o(.ARM.exidx.text.get_key_val) refers to nbutton.o(.text.get_key_val) for [Anonymous Symbol]
    nbutton.o(.text.get_key_short_press) refers to nbutton.o(.bss._button) for _button
    nbutton.o(.ARM.exidx.text.get_key_short_press) refers to nbutton.o(.text.get_key_short_press) for [Anonymous Symbol]
    nbutton.o(.text.get_key_long_press) refers to nbutton.o(.bss._button) for _button
    nbutton.o(.ARM.exidx.text.get_key_long_press) refers to nbutton.o(.text.get_key_long_press) for [Anonymous Symbol]
    nbutton.o(.text.key_process) refers to nbutton.o(.text.Read_Button_State_One) for Read_Button_State_One
    nbutton.o(.text.key_process) refers to imu_app.o(.text.IMU_App_Reset_Yaw) for IMU_App_Reset_Yaw
    nbutton.o(.text.key_process) refers to imu_app.o(.text.IMU_App_Load_Calibration) for IMU_App_Load_Calibration
    nbutton.o(.text.key_process) refers to led_app.o(.text.RGB_Set_Color) for RGB_Set_Color
    nbutton.o(.text.key_process) refers to imu_app.o(.text.IMU_App_Start_Drift_Test) for IMU_App_Start_Drift_Test
    nbutton.o(.text.key_process) refers to imu_app.o(.text.IMU_App_Save_Calibration) for IMU_App_Save_Calibration
    nbutton.o(.text.key_process) refers to imu_app.o(.text.IMU_App_Reset_Calibration) for IMU_App_Reset_Calibration
    nbutton.o(.text.key_process) refers to led_app.o(.text.RGB_All_Off) for RGB_All_Off
    nbutton.o(.text.key_process) refers to led_app.o(.text.RGB_Test_Sequence) for RGB_Test_Sequence
    nbutton.o(.text.key_process) refers to nbutton.o(.bss._button) for _button
    nbutton.o(.text.key_process) refers to ui.o(.bss.page_number) for page_number
    nbutton.o(.ARM.exidx.text.key_process) refers to nbutton.o(.text.key_process) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Init) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Init) refers to buzzer_app.o(.text.Buzzer_Init) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Work) refers to fflti.o(.text) for __aeabi_ui2f
    buzzer_app.o(.text.Buzzer_Work) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    buzzer_app.o(.text.Buzzer_Work) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    buzzer_app.o(.ARM.exidx.text.Buzzer_Work) refers to buzzer_app.o(.text.Buzzer_Work) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_On) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_On) refers to buzzer_app.o(.text.Buzzer_On) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Off) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Off) refers to buzzer_app.o(.text.Buzzer_Off) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Setup) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    buzzer_app.o(.text.Buzzer_Setup) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Setup) refers to buzzer_app.o(.text.Buzzer_Setup) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Tone) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Tone) refers to buzzer_app.o(.text.Buzzer_Tone) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Mode) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Mode) refers to buzzer_app.o(.text.Buzzer_Mode) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Beep) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Beep) refers to buzzer_app.o(.text.Buzzer_Beep) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Double_Beep) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Double_Beep) refers to buzzer_app.o(.text.Buzzer_Double_Beep) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Triple_Beep) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Triple_Beep) refers to buzzer_app.o(.text.Buzzer_Triple_Beep) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Long_Beep) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Long_Beep) refers to buzzer_app.o(.text.Buzzer_Long_Beep) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Alarm) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Alarm) refers to buzzer_app.o(.text.Buzzer_Alarm) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Success) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Success) refers to buzzer_app.o(.text.Buzzer_Success) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Error) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Error) refers to buzzer_app.o(.text.Buzzer_Error) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Key_Press) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Key_Press) refers to buzzer_app.o(.text.Buzzer_Key_Press) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Is_Working) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Is_Working) refers to buzzer_app.o(.text.Buzzer_Is_Working) for [Anonymous Symbol]
    buzzer_app.o(.text.Buzzer_Stop) refers to buzzer_app.o(.bss.buzzer) for buzzer
    buzzer_app.o(.ARM.exidx.text.Buzzer_Stop) refers to buzzer_app.o(.text.Buzzer_Stop) for [Anonymous Symbol]
    uart_app.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_Init) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_Init) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_Init) refers to uart_app.o(.text.UART0_App_Init) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_SendByte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_SendByte) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_SendByte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_SendByte) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    uart_app.o(.ARM.exidx.text.UART0_App_SendByte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_SendByte) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_SendByte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_SendByte) refers to uart_app.o(.text.UART0_App_SendByte) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_SendString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_SendString) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_SendString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_SendString) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    uart_app.o(.ARM.exidx.text.UART0_App_SendString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_SendString) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_SendString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_SendString) refers to uart_app.o(.text.UART0_App_SendString) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_SendNumber) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_SendNumber) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_SendNumber) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_SendNumber) refers to noretval__2sprintf.o(.text) for __2sprintf
    uart_app.o(.text.UART0_App_SendNumber) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    uart_app.o(.ARM.exidx.text.UART0_App_SendNumber) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_SendNumber) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_SendNumber) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_SendNumber) refers to uart_app.o(.text.UART0_App_SendNumber) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_SendBytes) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_SendBytes) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_SendBytes) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_SendBytes) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    uart_app.o(.ARM.exidx.text.UART0_App_SendBytes) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_SendBytes) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_SendBytes) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_SendBytes) refers to uart_app.o(.text.UART0_App_SendBytes) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_RxProcess) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_RxProcess) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_RxProcess) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_RxProcess) refers to system.o(.text.millis) for millis
    uart_app.o(.text.UART0_App_RxProcess) refers to uart_app.o(.bss.uart0_rx_index) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_RxProcess) refers to uart_app.o(.bss.uart0_rx_tick) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_RxProcess) refers to uart_app.o(.bss.uart0_rx_complete) for [Anonymous Symbol]
    uart_app.o(.ARM.exidx.text.UART0_App_RxProcess) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_RxProcess) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_RxProcess) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_RxProcess) refers to uart_app.o(.text.UART0_App_RxProcess) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_GetRxData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_GetRxData) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_GetRxData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_GetRxData) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    uart_app.o(.text.UART0_App_GetRxData) refers to rt_memclr.o(.text) for __aeabi_memclr4
    uart_app.o(.text.UART0_App_GetRxData) refers to uart_app.o(.bss.uart0_rx_complete) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_GetRxData) refers to uart_app.o(.bss.uart0_rx_index) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_GetRxData) refers to uart_app.o(.bss.uart0_rx_buffer) for [Anonymous Symbol]
    uart_app.o(.ARM.exidx.text.UART0_App_GetRxData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_GetRxData) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_GetRxData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_GetRxData) refers to uart_app.o(.text.UART0_App_GetRxData) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_ClearRxBuffer) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_ClearRxBuffer) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_ClearRxBuffer) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_ClearRxBuffer) refers to rt_memclr.o(.text) for __aeabi_memclr4
    uart_app.o(.text.UART0_App_ClearRxBuffer) refers to uart_app.o(.bss.uart0_rx_index) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_ClearRxBuffer) refers to uart_app.o(.bss.uart0_rx_buffer) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_ClearRxBuffer) refers to uart_app.o(.bss.uart0_rx_complete) for [Anonymous Symbol]
    uart_app.o(.ARM.exidx.text.UART0_App_ClearRxBuffer) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_ClearRxBuffer) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_ClearRxBuffer) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_ClearRxBuffer) refers to uart_app.o(.text.UART0_App_ClearRxBuffer) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_IsRxComplete) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_IsRxComplete) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_IsRxComplete) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_IsRxComplete) refers to uart_app.o(.bss.uart0_rx_complete) for [Anonymous Symbol]
    uart_app.o(.ARM.exidx.text.UART0_App_IsRxComplete) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_IsRxComplete) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_IsRxComplete) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_IsRxComplete) refers to uart_app.o(.text.UART0_App_IsRxComplete) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_RxCallback) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.text.UART0_App_RxCallback) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.text.UART0_App_RxCallback) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.text.UART0_App_RxCallback) refers to rt_memclr.o(.text) for __aeabi_memclr4
    uart_app.o(.text.UART0_App_RxCallback) refers to system.o(.text.millis) for millis
    uart_app.o(.text.UART0_App_RxCallback) refers to uart_app.o(.bss.uart0_rx_index) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_RxCallback) refers to uart_app.o(.bss.uart0_rx_buffer) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_RxCallback) refers to uart_app.o(.bss.uart0_rx_complete) for [Anonymous Symbol]
    uart_app.o(.text.UART0_App_RxCallback) refers to uart_app.o(.bss.uart0_rx_tick) for [Anonymous Symbol]
    uart_app.o(.ARM.exidx.text.UART0_App_RxCallback) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.ARM.exidx.text.UART0_App_RxCallback) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.ARM.exidx.text.UART0_App_RxCallback) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.ARM.exidx.text.UART0_App_RxCallback) refers to uart_app.o(.text.UART0_App_RxCallback) for [Anonymous Symbol]
    uart_app.o(.bss.uart0_rx_index) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.bss.uart0_rx_index) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.bss.uart0_rx_index) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.bss.uart0_rx_tick) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.bss.uart0_rx_tick) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.bss.uart0_rx_tick) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.bss.uart0_rx_complete) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.bss.uart0_rx_complete) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.bss.uart0_rx_complete) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_app.o(.bss.uart0_rx_buffer) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(.bss.uart0_rx_buffer) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    uart_app.o(.bss.uart0_rx_buffer) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    uart_process.o(.text.uart_process) refers to uart_app.o(.text.UART0_App_RxProcess) for UART0_App_RxProcess
    uart_process.o(.text.uart_process) refers to uart_app.o(.text.UART0_App_IsRxComplete) for UART0_App_IsRxComplete
    uart_process.o(.text.uart_process) refers to uart_app.o(.text.UART0_App_GetRxData) for UART0_App_GetRxData
    uart_process.o(.text.uart_process) refers to uart_app.o(.text.UART0_App_SendBytes) for UART0_App_SendBytes
    uart_process.o(.ARM.exidx.text.uart_process) refers to uart_process.o(.text.uart_process) for [Anonymous Symbol]
    motor_app.o(.text.Motor_App_Init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_app.o(.ARM.exidx.text.Motor_App_Init) refers to motor_app.o(.text.Motor_App_Init) for [Anonymous Symbol]
    motor_app.o(.text.car_stop) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_app.o(.ARM.exidx.text.car_stop) refers to motor_app.o(.text.car_stop) for [Anonymous Symbol]
    motor_app.o(.text.left_wheel_forward) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_app.o(.ARM.exidx.text.left_wheel_forward) refers to motor_app.o(.text.left_wheel_forward) for [Anonymous Symbol]
    motor_app.o(.text.left_wheel_backward) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_app.o(.ARM.exidx.text.left_wheel_backward) refers to motor_app.o(.text.left_wheel_backward) for [Anonymous Symbol]
    motor_app.o(.text.right_wheel_forward) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_app.o(.ARM.exidx.text.right_wheel_forward) refers to motor_app.o(.text.right_wheel_forward) for [Anonymous Symbol]
    motor_app.o(.text.right_wheel_backward) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_app.o(.ARM.exidx.text.right_wheel_backward) refers to motor_app.o(.text.right_wheel_backward) for [Anonymous Symbol]
    motor_app.o(.text.car_move) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_app.o(.ARM.exidx.text.car_move) refers to motor_app.o(.text.car_move) for [Anonymous Symbol]
    motor_app.o(.text.motor_demo) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_app.o(.text.motor_demo) refers to system.o(.text.delay_ms) for delay_ms
    motor_app.o(.ARM.exidx.text.motor_demo) refers to motor_app.o(.text.motor_demo) for [Anonymous Symbol]
    hui_app.o(.text.HUI_App_Init) refers to hui_app.o(.bss.hui_pin_states) for hui_pin_states
    hui_app.o(.ARM.exidx.text.HUI_App_Init) refers to hui_app.o(.text.HUI_App_Init) for [Anonymous Symbol]
    hui_app.o(.text.HUI_Read_All_Pins) refers to hui_app.o(.bss.hui_pin_states) for hui_pin_states
    hui_app.o(.ARM.exidx.text.HUI_Read_All_Pins) refers to hui_app.o(.text.HUI_Read_All_Pins) for [Anonymous Symbol]
    hui_app.o(.text.HUI_Get_Pin_State) refers to hui_app.o(.bss.hui_pin_states) for hui_pin_states
    hui_app.o(.ARM.exidx.text.HUI_Get_Pin_State) refers to hui_app.o(.text.HUI_Get_Pin_State) for [Anonymous Symbol]
    system.o(.text.SysTick_Handler) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.SysTick_Handler) refers to system.o(.text.SysTick_Handler) for [Anonymous Symbol]
    system.o(.text.ncontroller_set_priority) refers to system.o(.bss.irq_poriority) for irq_poriority
    system.o(.ARM.exidx.text.ncontroller_set_priority) refers to system.o(.text.ncontroller_set_priority) for [Anonymous Symbol]
    system.o(.text.micros) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.micros) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.micros) refers to system.o(.text.micros) for [Anonymous Symbol]
    system.o(.text.millis) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.millis) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.millis) refers to system.o(.text.millis) for [Anonymous Symbol]
    system.o(.text.delayMicroseconds) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.delayMicroseconds) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.delayMicroseconds) refers to system.o(.text.delayMicroseconds) for [Anonymous Symbol]
    system.o(.text.delay) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.delay) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.delay) refers to system.o(.text.delay) for [Anonymous Symbol]
    system.o(.text.delay_ms) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.delay_ms) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.delay_ms) refers to system.o(.text.delay_ms) for [Anonymous Symbol]
    system.o(.text.delay_us) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.delay_us) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.delay_us) refers to system.o(.text.delay_us) for [Anonymous Symbol]
    system.o(.text.Delay_Ms) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.Delay_Ms) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.Delay_Ms) refers to system.o(.text.Delay_Ms) for [Anonymous Symbol]
    system.o(.text.Delay_Us) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.Delay_Us) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.Delay_Us) refers to system.o(.text.Delay_Us) for [Anonymous Symbol]
    system.o(.text.get_systime) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.get_systime) refers to fflti.o(.text) for __aeabi_ui2f
    system.o(.text.get_systime) refers to fdiv.o(.text) for __aeabi_fdiv
    system.o(.text.get_systime) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    system.o(.text.get_systime) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    system.o(.text.get_systime) refers to ffixi.o(.text) for __aeabi_f2iz
    system.o(.text.get_systime) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.get_systime) refers to system.o(.text.get_systime) for [Anonymous Symbol]
    system.o(.text.get_systime_ms) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system.o(.text.get_systime_ms) refers to fflti.o(.text) for __aeabi_ui2f
    system.o(.text.get_systime_ms) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.get_systime_ms) refers to system.o(.text.get_systime_ms) for [Anonymous Symbol]
    system.o(.text.get_systick_ms) refers to system.o(.bss.sysTickUptime) for sysTickUptime
    system.o(.ARM.exidx.text.get_systick_ms) refers to system.o(.text.get_systick_ms) for [Anonymous Symbol]
    system.o(.text.NMI_Handler) refers to system.o(.bss.nmi_cnt) for nmi_cnt
    system.o(.ARM.exidx.text.NMI_Handler) refers to system.o(.text.NMI_Handler) for [Anonymous Symbol]
    system.o(.text.HardFault_Handler) refers to system.o(.bss.hf_cnt) for hf_cnt
    system.o(.ARM.exidx.text.HardFault_Handler) refers to system.o(.text.HardFault_Handler) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_WrDat) refers to oled.o(.text.OLED_WrDat) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_WrCmd) refers to oled.o(.text.OLED_WrCmd) for [Anonymous Symbol]
    oled.o(.text.OLED_Set_Pos) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.ARM.exidx.text.OLED_Set_Pos) refers to oled.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    oled.o(.text.OLED_Fill) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.text.OLED_Fill) refers to oled.o(.text.OLED_WrDat) for OLED_WrDat
    oled.o(.ARM.exidx.text.OLED_Fill) refers to oled.o(.text.OLED_Fill) for [Anonymous Symbol]
    oled.o(.text.OLED_CLS) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.ARM.exidx.text.OLED_CLS) refers to oled.o(.text.OLED_CLS) for [Anonymous Symbol]
    oled.o(.text.OLED_Init_I2C) refers to oled.o(.text.OLED_Fill) for OLED_Fill
    oled.o(.ARM.exidx.text.OLED_Init_I2C) refers to oled.o(.text.OLED_Init_I2C) for [Anonymous Symbol]
    oled.o(.text.LCD_Set_Pos) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.ARM.exidx.text.LCD_Set_Pos) refers to oled.o(.text.LCD_Set_Pos) for [Anonymous Symbol]
    oled.o(.text.LCD_Fill) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.text.LCD_Fill) refers to oled.o(.text.OLED_WrDat) for OLED_WrDat
    oled.o(.ARM.exidx.text.LCD_Fill) refers to oled.o(.text.LCD_Fill) for [Anonymous Symbol]
    oled.o(.text.LCD_CLS) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.ARM.exidx.text.LCD_CLS) refers to oled.o(.text.LCD_CLS) for [Anonymous Symbol]
    oled.o(.text.LCD_P6x8Str) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.text.LCD_P6x8Str) refers to oled.o(.text.OLED_WrDat) for OLED_WrDat
    oled.o(.text.LCD_P6x8Str) refers to oled.o(.rodata.F6x8) for F6x8
    oled.o(.ARM.exidx.text.LCD_P6x8Str) refers to oled.o(.text.LCD_P6x8Str) for [Anonymous Symbol]
    oled.o(.text.LCD_P6x8Char) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.text.LCD_P6x8Char) refers to oled.o(.text.OLED_WrDat) for OLED_WrDat
    oled.o(.text.LCD_P6x8Char) refers to oled.o(.rodata.F6x8) for F6x8
    oled.o(.ARM.exidx.text.LCD_P6x8Char) refers to oled.o(.text.LCD_P6x8Char) for [Anonymous Symbol]
    oled.o(.text.write_6_8_number) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    oled.o(.text.write_6_8_number) refers to oled.o(.text.LCD_P6x8Char) for LCD_P6x8Char
    oled.o(.text.write_6_8_number) refers to ffixi.o(.text) for __aeabi_f2iz
    oled.o(.text.write_6_8_number) refers to fflti.o(.text) for __aeabi_i2f
    oled.o(.text.write_6_8_number) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    oled.o(.text.write_6_8_number) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.write_6_8_number) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    oled.o(.text.write_6_8_number) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    oled.o(.text.write_6_8_number) refers to oled.o(.text.LCD_P6x8Str) for LCD_P6x8Str
    oled.o(.ARM.exidx.text.write_6_8_number) refers to oled.o(.text.write_6_8_number) for [Anonymous Symbol]
    oled.o(.text.write_6_8_number_f1) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    oled.o(.text.write_6_8_number_f1) refers to oled.o(.text.LCD_P6x8Char) for LCD_P6x8Char
    oled.o(.text.write_6_8_number_f1) refers to ffixi.o(.text) for __aeabi_f2iz
    oled.o(.text.write_6_8_number_f1) refers to fflti.o(.text) for __aeabi_i2f
    oled.o(.text.write_6_8_number_f1) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    oled.o(.text.write_6_8_number_f1) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.write_6_8_number_f1) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    oled.o(.text.write_6_8_number_f1) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    oled.o(.text.write_6_8_number_f1) refers to oled.o(.text.LCD_P6x8Str) for LCD_P6x8Str
    oled.o(.ARM.exidx.text.write_6_8_number_f1) refers to oled.o(.text.write_6_8_number_f1) for [Anonymous Symbol]
    oled.o(.text.LCD_P8x16Str) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.text.LCD_P8x16Str) refers to oled.o(.text.OLED_WrDat) for OLED_WrDat
    oled.o(.text.LCD_P8x16Str) refers to oled.o(.rodata.F8X16) for F8X16
    oled.o(.ARM.exidx.text.LCD_P8x16Str) refers to oled.o(.text.LCD_P8x16Str) for [Anonymous Symbol]
    oled.o(.text.LCD_P8x16Char) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.text.LCD_P8x16Char) refers to oled.o(.text.OLED_WrDat) for OLED_WrDat
    oled.o(.text.LCD_P8x16Char) refers to oled.o(.rodata.F8X16) for F8X16
    oled.o(.ARM.exidx.text.LCD_P8x16Char) refers to oled.o(.text.LCD_P8x16Char) for [Anonymous Symbol]
    oled.o(.text.write_8_16_number) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    oled.o(.text.write_8_16_number) refers to oled.o(.text.LCD_P8x16Char) for LCD_P8x16Char
    oled.o(.text.write_8_16_number) refers to ffixi.o(.text) for __aeabi_f2iz
    oled.o(.text.write_8_16_number) refers to fflti.o(.text) for __aeabi_i2f
    oled.o(.text.write_8_16_number) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    oled.o(.text.write_8_16_number) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.write_8_16_number) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    oled.o(.text.write_8_16_number) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    oled.o(.text.write_8_16_number) refers to oled.o(.text.LCD_P8x16Str) for LCD_P8x16Str
    oled.o(.ARM.exidx.text.write_8_16_number) refers to oled.o(.text.write_8_16_number) for [Anonymous Symbol]
    oled.o(.text.write_16_16_CN) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.ARM.exidx.text.write_16_16_CN) refers to oled.o(.text.write_16_16_CN) for [Anonymous Symbol]
    oled.o(.text.LCD_clear_L) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.ARM.exidx.text.LCD_clear_L) refers to oled.o(.text.LCD_clear_L) for [Anonymous Symbol]
    oled.o(.text.Draw_Logo) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    oled.o(.text.Draw_Logo) refers to oled.o(.text.OLED_WrDat) for OLED_WrDat
    oled.o(.text.Draw_Logo) refers to system.o(.text.Delay_Ms) for Delay_Ms
    oled.o(.text.Draw_Logo) refers to oled.o(.text.LCD_CLS) for LCD_CLS
    oled.o(.text.Draw_Logo) refers to oled.o(.rodata.NC_Logo) for NC_Logo
    oled.o(.ARM.exidx.text.Draw_Logo) refers to oled.o(.text.Draw_Logo) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to system.o(.text.delay_ms) for delay_ms
    oled.o(.text.OLED_Init) refers to ssd1306.o(.text.ssd1306_begin) for ssd1306_begin
    oled.o(.text.OLED_Init) refers to oled.o(.text.Draw_Logo) for Draw_Logo
    oled.o(.text.OLED_Init) refers to oled.o(.text.LCD_CLS) for LCD_CLS
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.display_6_8_number) refers to oled.o(.text.write_6_8_number) for write_6_8_number
    oled.o(.ARM.exidx.text.display_6_8_number) refers to oled.o(.text.display_6_8_number) for [Anonymous Symbol]
    oled.o(.text.display_6_8_number_pro) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    oled.o(.text.display_6_8_number_pro) refers to oled.o(.text.LCD_P6x8Char) for LCD_P6x8Char
    oled.o(.text.display_6_8_number_pro) refers to oled.o(.text.write_6_8_number) for write_6_8_number
    oled.o(.ARM.exidx.text.display_6_8_number_pro) refers to oled.o(.text.display_6_8_number_pro) for [Anonymous Symbol]
    oled.o(.text.display_6_8_string) refers to oled.o(.text.LCD_P6x8Str) for LCD_P6x8Str
    oled.o(.ARM.exidx.text.display_6_8_string) refers to oled.o(.text.display_6_8_string) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_width) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_width) refers to ssd1306.o(.text.ssd1306_width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_height) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_height) refers to ssd1306.o(.text.ssd1306_height) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.rodata.cst8) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.set_rotation) refers to ssd1306.o(.text.set_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_command) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_command) refers to ssd1306.o(.text.ssd1306_command) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss._vccstate) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss._cp437) for _cp437
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_begin) refers to ssd1306.o(.text.ssd1306_begin) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_pixel) refers to ssd1306.o(.text.ssd1306_draw_pixel) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_invert_display) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_invert_display) refers to ssd1306.o(.text.ssd1306_invert_display) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_start_scroll_right) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_right) refers to ssd1306.o(.text.ssd1306_start_scroll_right) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_start_scroll_left) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_left) refers to ssd1306.o(.text.ssd1306_start_scroll_left) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_start_scroll_diag_right) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_diag_right) refers to ssd1306.o(.text.ssd1306_start_scroll_diag_right) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_start_scroll_diag_left) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_diag_left) refers to ssd1306.o(.text.ssd1306_start_scroll_diag_left) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_stop_scroll) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_stop_scroll) refers to ssd1306.o(.text.ssd1306_stop_scroll) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_dim) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.text.ssd1306_dim) refers to ssd1306.o(.bss._vccstate) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_dim) refers to ssd1306.o(.text.ssd1306_dim) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_data) refers to oled.o(.text.OLED_WrDat) for OLED_WrDat
    ssd1306.o(.ARM.exidx.text.ssd1306_data) refers to ssd1306.o(.text.ssd1306_data) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_display) refers to oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.text.ssd1306_display) refers to ssd1306.o(.text.draw_oled) for draw_oled
    ssd1306.o(.ARM.exidx.text.ssd1306_display) refers to ssd1306.o(.text.ssd1306_display) for [Anonymous Symbol]
    ssd1306.o(.text.draw_oled) refers to oled.o(.text.OLED_Set_Pos) for OLED_Set_Pos
    ssd1306.o(.text.draw_oled) refers to oled.o(.text.OLED_WrDat) for OLED_WrDat
    ssd1306.o(.text.draw_oled) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.draw_oled) refers to ssd1306.o(.text.draw_oled) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_clear_display) refers to rt_memclr.o(.text) for __aeabi_memclr4
    ssd1306.o(.text.ssd1306_clear_display) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_clear_display) refers to ssd1306.o(.text.ssd1306_clear_display) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.text.ssd1306_draw_fast_vline_internal) for ssd1306_draw_fast_vline_internal
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.text.ssd1306_draw_fast_hline) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline_internal) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline_internal) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline_internal) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_vline_internal) refers to ssd1306.o(.text.ssd1306_draw_fast_vline_internal) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline_internal) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline_internal) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline_internal) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_hline_internal) refers to ssd1306.o(.text.ssd1306_draw_fast_hline_internal) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.text.ssd1306_draw_fast_vline_internal) for ssd1306_draw_fast_vline_internal
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_circle) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_circle) refers to ssd1306.o(.text.ssd1306_draw_circle) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_circle_helper) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_circle_helper) refers to ssd1306.o(.text.ssd1306_draw_circle_helper) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_circle) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.text.ssd1306_fill_circle) refers to ssd1306.o(.text.ssd1306_fill_circle_helper) for ssd1306_fill_circle_helper
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_circle) refers to ssd1306.o(.text.ssd1306_fill_circle) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_circle_helper) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_circle_helper) refers to ssd1306.o(.text.ssd1306_fill_circle_helper) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_line) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_line) refers to ssd1306.o(.text.ssd1306_draw_line) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_hline) for ssd1306_draw_fast_hline
    ssd1306.o(.text.ssd1306_draw_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_rect) refers to ssd1306.o(.text.ssd1306_draw_rect) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_rect) refers to ssd1306.o(.text.ssd1306_fill_rect) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_screen) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.text.ssd1306_fill_screen) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_screen) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_screen) refers to ssd1306.o(.text.ssd1306_fill_screen) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_round_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_hline) for ssd1306_draw_fast_hline
    ssd1306.o(.text.ssd1306_draw_round_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.text.ssd1306_draw_round_rect) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_round_rect) refers to ssd1306.o(.text.ssd1306_draw_round_rect) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_round_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_round_rect) refers to ssd1306.o(.text.ssd1306_fill_round_rect) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_triangle) refers to ssd1306.o(.text.ssd1306_draw_line) for ssd1306_draw_line
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_triangle) refers to ssd1306.o(.text.ssd1306_draw_triangle) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_triangle) refers to ssd1306.o(.text.ssd1306_draw_fast_hline) for ssd1306_draw_fast_hline
    ssd1306.o(.text.ssd1306_fill_triangle) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_triangle) refers to ssd1306.o(.text.ssd1306_fill_triangle) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_bitmap) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_bitmap) refers to ssd1306.o(.text.ssd1306_draw_bitmap) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_bitmap_bg) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_bitmap_bg) refers to ssd1306.o(.text.ssd1306_draw_bitmap_bg) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_xbitmap) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_xbitmap) refers to ssd1306.o(.text.ssd1306_draw_xbitmap) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.text.ssd1306_draw_char) for ssd1306_draw_char
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_write) refers to ssd1306.o(.text.ssd1306_write) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.bss._cp437) for _cp437
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.rodata.font) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_char) refers to ssd1306.o(.text.ssd1306_draw_char) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_cursor) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_cursor) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_cursor) refers to ssd1306.o(.text.ssd1306_set_cursor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_get_cursor_x) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_get_cursor_x) refers to ssd1306.o(.text.ssd1306_get_cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_get_cursor_y) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_get_cursor_y) refers to ssd1306.o(.text.ssd1306_get_cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textsize) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_textsize) refers to ssd1306.o(.text.ssd1306_set_textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textcolor) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textcolor) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_textcolor) refers to ssd1306.o(.text.ssd1306_set_textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textcolor_bg) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textcolor_bg) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_textcolor_bg) refers to ssd1306.o(.text.ssd1306_set_textcolor_bg) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textwrap) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.ARM.exidx.text.ssd1306_set_textwrap) refers to ssd1306.o(.text.ssd1306_set_textwrap) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_get_rotation) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_get_rotation) refers to ssd1306.o(.text.ssd1306_get_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.rodata.cst8) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_rotation) refers to ssd1306.o(.text.ssd1306_set_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_cp437) refers to ssd1306.o(.bss._cp437) for _cp437
    ssd1306.o(.ARM.exidx.text.ssd1306_cp437) refers to ssd1306.o(.text.ssd1306_cp437) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.text.ssd1306_draw_char) for ssd1306_draw_char
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.ARM.exidx.text.ssd1306_putstring) refers to ssd1306.o(.text.ssd1306_putstring) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.text.ssd1306_draw_char) for ssd1306_draw_char
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.ARM.exidx.text.ssd1306_puts) refers to ssd1306.o(.text.ssd1306_puts) for [Anonymous Symbol]
    ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ntimer.o(.ARM.exidx.text.timer_irq_config) refers to ntimer.o(.text.timer_irq_config) for [Anonymous Symbol]
    ntimer.o(.ARM.exidx.text.timer_pwm_config) refers to ntimer.o(.text.timer_pwm_config) for [Anonymous Symbol]
    ntimer.o(.text.TIMG0_IRQHandler) refers to system.o(.text.get_systime) for get_systime
    ntimer.o(.text.TIMG0_IRQHandler) refers to ntimer.o(.bss.t_g0) for t_g0
    ntimer.o(.ARM.exidx.text.TIMG0_IRQHandler) refers to ntimer.o(.text.TIMG0_IRQHandler) for [Anonymous Symbol]
    ntimer.o(.text.TIMG6_IRQHandler) refers to system.o(.text.get_systime) for get_systime
    ntimer.o(.text.TIMG6_IRQHandler) refers to buzzer_app.o(.text.Buzzer_Work) for Buzzer_Work
    ntimer.o(.text.TIMG6_IRQHandler) refers to ntimer.o(.bss.t_g1) for t_g1
    ntimer.o(.text.TIMG6_IRQHandler) refers to buzzer_app.o(.bss.buzzer) for buzzer
    ntimer.o(.ARM.exidx.text.TIMG6_IRQHandler) refers to ntimer.o(.text.TIMG6_IRQHandler) for [Anonymous Symbol]
    ntimer.o(.text.TIMG8_IRQHandler) refers to system.o(.text.get_systime) for get_systime
    ntimer.o(.text.TIMG8_IRQHandler) refers to ntimer.o(.bss.t_g8) for t_g8
    ntimer.o(.ARM.exidx.text.TIMG8_IRQHandler) refers to ntimer.o(.text.TIMG8_IRQHandler) for [Anonymous Symbol]
    ntimer.o(.text.TIMG12_IRQHandler) refers to system.o(.text.get_systime) for get_systime
    ntimer.o(.text.TIMG12_IRQHandler) refers to imu_app.o(.text.IMU_App_Update) for IMU_App_Update
    ntimer.o(.text.TIMG12_IRQHandler) refers to ntimer.o(.bss.t_g12) for t_g12
    ntimer.o(.ARM.exidx.text.TIMG12_IRQHandler) refers to ntimer.o(.text.TIMG12_IRQHandler) for [Anonymous Symbol]
    ntimer.o(.text.Reserved_PWM5_Output) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ntimer.o(.ARM.exidx.text.Reserved_PWM5_Output) refers to ntimer.o(.text.Reserved_PWM5_Output) for [Anonymous Symbol]
    ntimer.o(.text.Reserved_PWM6_Output) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ntimer.o(.ARM.exidx.text.Reserved_PWM6_Output) refers to ntimer.o(.text.Reserved_PWM6_Output) for [Anonymous Symbol]
    ntimer.o(.text.Reserved_PWM7_Output) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ntimer.o(.ARM.exidx.text.Reserved_PWM7_Output) refers to ntimer.o(.text.Reserved_PWM7_Output) for [Anonymous Symbol]
    ntimer.o(.text.Reserved_PWM8_Output) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ntimer.o(.ARM.exidx.text.Reserved_PWM8_Output) refers to ntimer.o(.text.Reserved_PWM8_Output) for [Anonymous Symbol]
    nuart.o(.ARM.exidx.text.usart_irq_config) refers to nuart.o(.text.usart_irq_config) for [Anonymous Symbol]
    nuart.o(.text.UART0_IRQHandler) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for DL_UART_receiveDataCheck
    nuart.o(.text.UART0_IRQHandler) refers to uart_app.o(.text.UART0_App_RxCallback) for UART0_App_RxCallback
    nuart.o(.ARM.exidx.text.UART0_IRQHandler) refers to nuart.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    nuart.o(.ARM.exidx.text.UART1_IRQHandler) refers to nuart.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    nuart.o(.text.UART2_IRQHandler) refers to system.o(.text.get_systime) for get_systime
    nuart.o(.text.UART2_IRQHandler) refers to nuart.o(.bss.uart2_dma_t) for uart2_dma_t
    nuart.o(.ARM.exidx.text.UART2_IRQHandler) refers to nuart.o(.text.UART2_IRQHandler) for [Anonymous Symbol]
    nuart.o(.ARM.exidx.text.UART3_IRQHandler) refers to nuart.o(.text.UART3_IRQHandler) for [Anonymous Symbol]
    nuart.o(.text.usart0_send_string) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    nuart.o(.ARM.exidx.text.usart0_send_string) refers to nuart.o(.text.usart0_send_string) for [Anonymous Symbol]
    nuart.o(.text.usart0_send_bytes) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    nuart.o(.ARM.exidx.text.usart0_send_bytes) refers to nuart.o(.text.usart0_send_bytes) for [Anonymous Symbol]
    nuart.o(.text.fputc) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    nuart.o(.ARM.exidx.text.fputc) refers to nuart.o(.text.fputc) for [Anonymous Symbol]
    nuart.o(.text.usart1_send_bytes) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    nuart.o(.ARM.exidx.text.usart1_send_bytes) refers to nuart.o(.text.usart1_send_bytes) for [Anonymous Symbol]
    nuart.o(.text.UART_SendBytes) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    nuart.o(.ARM.exidx.text.UART_SendBytes) refers to nuart.o(.text.UART_SendBytes) for [Anonymous Symbol]
    nuart.o(.text.UART_SendByte) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    nuart.o(.ARM.exidx.text.UART_SendByte) refers to nuart.o(.text.UART_SendByte) for [Anonymous Symbol]
    ni2c.o(.text.I2C_WriteReg) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    ni2c.o(.text.I2C_WriteReg) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    ni2c.o(.text.I2C_WriteReg) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    ni2c.o(.ARM.exidx.text.I2C_WriteReg) refers to ni2c.o(.text.I2C_WriteReg) for [Anonymous Symbol]
    ni2c.o(.text.I2C_ReadReg) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    ni2c.o(.text.I2C_ReadReg) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    ni2c.o(.ARM.exidx.text.I2C_ReadReg) refers to ni2c.o(.text.I2C_ReadReg) for [Anonymous Symbol]
    ni2c.o(.text.single_writei2c) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    ni2c.o(.text.single_writei2c) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    ni2c.o(.ARM.exidx.text.single_writei2c) refers to ni2c.o(.text.single_writei2c) for [Anonymous Symbol]
    ni2c.o(.text.single_readi2c) refers to ni2c.o(.text.I2C_ReadReg) for I2C_ReadReg
    ni2c.o(.ARM.exidx.text.single_readi2c) refers to ni2c.o(.text.single_readi2c) for [Anonymous Symbol]
    ni2c.o(.text.i2creadnbyte) refers to ni2c.o(.text.I2C_ReadReg) for I2C_ReadReg
    ni2c.o(.ARM.exidx.text.i2creadnbyte) refers to ni2c.o(.text.i2creadnbyte) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Init) refers to ni2c.o(.text.single_writei2c) for single_writei2c
    icm20608.o(.text.ICM20608_Init) refers to system.o(.text.delay_ms) for delay_ms
    icm20608.o(.text.ICM20608_Init) refers to ni2c.o(.text.single_readi2c) for single_readi2c
    icm20608.o(.text.ICM20608_Init) refers to filter.o(.text.set_cutoff_frequency) for set_cutoff_frequency
    icm20608.o(.text.ICM20608_Init) refers to fusionoffset.o(.text.FusionOffsetInitialise) for FusionOffsetInitialise
    icm20608.o(.text.ICM20608_Init) refers to calibration.o(.text.Calibration_Init) for Calibration_Init
    icm20608.o(.text.ICM20608_Init) refers to calibration.o(.text.Calibration_Get_Status) for Calibration_Get_Status
    icm20608.o(.text.ICM20608_Init) refers to calibration.o(.text.Calibration_Get_Gyro_Offset) for Calibration_Get_Gyro_Offset
    icm20608.o(.text.ICM20608_Init) refers to icm20608.o(.bss.imu_data) for imu_data
    icm20608.o(.text.ICM20608_Init) refers to icm20608.o(.bss.gyro_lpf_buf) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Init) refers to icm20608.o(.bss.accel_lpf_buf) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Init) refers to icm20608.o(.bss.gyro_lpf_param) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Init) refers to icm20608.o(.bss.accel_lpf_param) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Init) refers to icm20608.o(.bss.fusion_offset) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Init) refers to icm20608.o(.bss.fusion_offset_init) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Init) refers to icm20608.o(.bss.gyro_offset) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Init) refers to icm20608.o(.bss.gyro_calib_done) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Init) refers to icm20608.o(.text.ICM20608_Init) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to system.o(.text.delay_ms) for delay_ms
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to ni2c.o(.text.i2creadnbyte) for i2creadnbyte
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to fflti.o(.text) for __aeabi_i2f
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to fdiv.o(.text) for __aeabi_fdiv
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to calibration.o(.text.Calibration_Set_Gyro_Offset) for Calibration_Set_Gyro_Offset
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to calibration.o(.text.Calibration_Save) for Calibration_Save
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to icm20608.o(.bss.imu_data) for imu_data
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to icm20608.o(.bss.gyro_calib_done) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to icm20608.o(.bss.gyro_calib_in_progress) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Calibrate_Gyro) refers to icm20608.o(.bss.gyro_offset) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Calibrate_Gyro) refers to icm20608.o(.text.ICM20608_Calibrate_Gyro) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Calib_Status) refers to icm20608.o(.bss.gyro_calib_done) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Calib_Status) refers to icm20608.o(.bss.gyro_calib_in_progress) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Get_Calib_Status) refers to icm20608.o(.text.ICM20608_Get_Calib_Status) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Gyro_Offset) refers to icm20608.o(.bss.fusion_offset_init) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Gyro_Offset) refers to icm20608.o(.bss.gyro_offset) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Gyro_Offset) refers to icm20608.o(.bss.fusion_offset) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Get_Gyro_Offset) refers to icm20608.o(.text.ICM20608_Get_Gyro_Offset) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Fusion_Status) refers to icm20608.o(.bss.fusion_offset_init) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Get_Fusion_Status) refers to icm20608.o(.text.ICM20608_Get_Fusion_Status) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Fusion_Timer) refers to icm20608.o(.bss.fusion_offset) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Fusion_Timer) refers to icm20608.o(.bss.fusion_offset_init) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Get_Fusion_Timer) refers to icm20608.o(.text.ICM20608_Get_Fusion_Timer) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Is_Temperature_Stable) refers to icm20608.o(.bss.temperature_stable_flag) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Is_Temperature_Stable) refers to icm20608.o(.text.ICM20608_Is_Temperature_Stable) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Temperature_Range) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    icm20608.o(.text.ICM20608_Get_Temperature_Range) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    icm20608.o(.text.ICM20608_Get_Temperature_Range) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    icm20608.o(.text.ICM20608_Get_Temperature_Range) refers to icm20608.o(.bss.temp_history_full) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Temperature_Range) refers to icm20608.o(.bss.temp_history) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Get_Temperature_Range) refers to icm20608.o(.text.ICM20608_Get_Temperature_Range) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Yaw_Quality) refers to icm20608.o(.data.yaw_quality_score) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Get_Yaw_Quality) refers to icm20608.o(.text.ICM20608_Get_Yaw_Quality) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate) refers to icm20608.o(.bss.yaw_drift_rate) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Get_Yaw_Drift_Rate) refers to icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Reset_Yaw) refers to icm20608.o(.bss.yaw_reset_request) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Reset_Yaw) refers to icm20608.o(.text.ICM20608_Reset_Yaw) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Save_Calibration) refers to calibration.o(.text.Calibration_Set_Gyro_Offset) for Calibration_Set_Gyro_Offset
    icm20608.o(.text.ICM20608_Save_Calibration) refers to calibration.o(.text.Calibration_Save) for Calibration_Save
    icm20608.o(.text.ICM20608_Save_Calibration) refers to icm20608.o(.bss.gyro_calib_done) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Save_Calibration) refers to icm20608.o(.bss.gyro_offset) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Save_Calibration) refers to icm20608.o(.text.ICM20608_Save_Calibration) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Load_Calibration) refers to calibration.o(.text.Calibration_Load) for Calibration_Load
    icm20608.o(.text.ICM20608_Load_Calibration) refers to calibration.o(.text.Calibration_Get_Status) for Calibration_Get_Status
    icm20608.o(.text.ICM20608_Load_Calibration) refers to calibration.o(.text.Calibration_Get_Gyro_Offset) for Calibration_Get_Gyro_Offset
    icm20608.o(.text.ICM20608_Load_Calibration) refers to icm20608.o(.bss.gyro_offset) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Load_Calibration) refers to icm20608.o(.bss.gyro_calib_done) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Load_Calibration) refers to icm20608.o(.text.ICM20608_Load_Calibration) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Reset_Calibration) refers to calibration.o(.text.Calibration_Reset) for Calibration_Reset
    icm20608.o(.text.ICM20608_Reset_Calibration) refers to icm20608.o(.bss.gyro_offset) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Reset_Calibration) refers to icm20608.o(.bss.gyro_calib_done) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Reset_Calibration) refers to icm20608.o(.text.ICM20608_Reset_Calibration) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Calibration_Status) refers to calibration.o(.text.Calibration_Get_Status) for Calibration_Get_Status
    icm20608.o(.ARM.exidx.text.ICM20608_Get_Calibration_Status) refers to icm20608.o(.text.ICM20608_Get_Calibration_Status) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to ni2c.o(.text.i2creadnbyte) for i2creadnbyte
    icm20608.o(.text.ICM20608_Read_Data) refers to fflti.o(.text) for __aeabi_i2f
    icm20608.o(.text.ICM20608_Read_Data) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    icm20608.o(.text.ICM20608_Read_Data) refers to fdiv.o(.text) for __aeabi_fdiv
    icm20608.o(.text.ICM20608_Read_Data) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    icm20608.o(.text.ICM20608_Read_Data) refers to filter.o(.text.LPButterworth) for LPButterworth
    icm20608.o(.text.ICM20608_Read_Data) refers to fusionoffset.o(.text.FusionOffsetUpdate) for FusionOffsetUpdate
    icm20608.o(.text.ICM20608_Read_Data) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    icm20608.o(.text.ICM20608_Read_Data) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    icm20608.o(.text.ICM20608_Read_Data) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    icm20608.o(.text.ICM20608_Read_Data) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    icm20608.o(.text.ICM20608_Read_Data) refers to calibration.o(.text.Calibration_Update) for Calibration_Update
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.imu_data) for imu_data
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.gyro_lpf_buf) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.gyro_lpf_param) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.fusion_offset_init) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.gyro_calib_done) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.gyro_offset) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.fusion_offset) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.accel_lpf_buf) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.accel_lpf_param) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.temp_history_index) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.temp_history) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.temp_history_full) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.temp_stable_timer) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Read_Data) refers to icm20608.o(.bss.temperature_stable_flag) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Read_Data) refers to icm20608.o(.text.ICM20608_Read_Data) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Update_Angles) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    icm20608.o(.text.ICM20608_Update_Angles) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    icm20608.o(.text.ICM20608_Update_Angles) refers to sqrtf.o(i.sqrtf) for sqrtf
    icm20608.o(.text.ICM20608_Update_Angles) refers to atan2f.o(i.atan2f) for atan2f
    icm20608.o(.text.ICM20608_Update_Angles) refers to rt_memclr.o(.text) for __aeabi_memclr4
    icm20608.o(.text.ICM20608_Update_Angles) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    icm20608.o(.text.ICM20608_Update_Angles) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    icm20608.o(.text.ICM20608_Update_Angles) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    icm20608.o(.text.ICM20608_Update_Angles) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    icm20608.o(.text.ICM20608_Update_Angles) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    icm20608.o(.text.ICM20608_Update_Angles) refers to fflti.o(.text) for __aeabi_i2f
    icm20608.o(.text.ICM20608_Update_Angles) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    icm20608.o(.text.ICM20608_Update_Angles) refers to fdiv.o(.text) for __aeabi_fdiv
    icm20608.o(.text.ICM20608_Update_Angles) refers to ffixui.o(.text) for __aeabi_f2uiz
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.bss.imu_data) for imu_data
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.bss.temperature_stable_flag) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.bss.yaw_history_full) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.bss.yaw_reset_request) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.bss.ICM20608_Update_Angles.prev_gyro_z) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.bss.yaw_lpf_output) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.bss.yaw_history_index) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.bss.yaw_history) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.bss.yaw_drift_rate) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Update_Angles) refers to icm20608.o(.data.yaw_quality_score) for [Anonymous Symbol]
    icm20608.o(.ARM.exidx.text.ICM20608_Update_Angles) refers to icm20608.o(.text.ICM20608_Update_Angles) for [Anonymous Symbol]
    icm20608.o(.text.ICM20608_Get_Angles) refers to icm20608.o(.bss.imu_data) for imu_data
    icm20608.o(.ARM.exidx.text.ICM20608_Get_Angles) refers to icm20608.o(.text.ICM20608_Get_Angles) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.Encoder_Init) refers to encoder_driver.o(.text.Encoder_Init) for [Anonymous Symbol]
    encoder_driver.o(.text.QEI0_IRQHandler) refers to encoder_driver.o(.data.NEncoder) for NEncoder
    encoder_driver.o(.text.QEI0_IRQHandler) refers to encoder_driver.o(.data.trackless_motor) for trackless_motor
    encoder_driver.o(.ARM.exidx.text.QEI0_IRQHandler) refers to encoder_driver.o(.text.QEI0_IRQHandler) for [Anonymous Symbol]
    encoder_driver.o(.text.QEI1_IRQHandler) refers to encoder_driver.o(.data.NEncoder) for NEncoder
    encoder_driver.o(.text.QEI1_IRQHandler) refers to encoder_driver.o(.data.trackless_motor) for trackless_motor
    encoder_driver.o(.ARM.exidx.text.QEI1_IRQHandler) refers to encoder_driver.o(.text.QEI1_IRQHandler) for [Anonymous Symbol]
    encoder_driver.o(.text.get_left_motor_speed) refers to filter.o(.text.set_cutoff_frequency) for set_cutoff_frequency
    encoder_driver.o(.text.get_left_motor_speed) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    encoder_driver.o(.text.get_left_motor_speed) refers to fflti.o(.text) for __aeabi_i2f
    encoder_driver.o(.text.get_left_motor_speed) refers to fdiv.o(.text) for __aeabi_fdiv
    encoder_driver.o(.text.get_left_motor_speed) refers to filter.o(.text.LPButterworth) for LPButterworth
    encoder_driver.o(.text.get_left_motor_speed) refers to encoder_driver.o(.bss.get_left_motor_speed.cnt1) for [Anonymous Symbol]
    encoder_driver.o(.text.get_left_motor_speed) refers to encoder_driver.o(.data.NEncoder) for NEncoder
    encoder_driver.o(.text.get_left_motor_speed) refers to encoder_driver.o(.bss.filter_initialized) for [Anonymous Symbol]
    encoder_driver.o(.text.get_left_motor_speed) refers to encoder_driver.o(.bss.speed_filter_param) for [Anonymous Symbol]
    encoder_driver.o(.text.get_left_motor_speed) refers to encoder_driver.o(.data.trackless_motor) for trackless_motor
    encoder_driver.o(.text.get_left_motor_speed) refers to encoder_driver.o(.bss.left_speed_filter_buf) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.get_left_motor_speed) refers to encoder_driver.o(.text.get_left_motor_speed) for [Anonymous Symbol]
    encoder_driver.o(.text.get_right_motor_speed) refers to fflti.o(.text) for __aeabi_i2f
    encoder_driver.o(.text.get_right_motor_speed) refers to fdiv.o(.text) for __aeabi_fdiv
    encoder_driver.o(.text.get_right_motor_speed) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    encoder_driver.o(.text.get_right_motor_speed) refers to filter.o(.text.LPButterworth) for LPButterworth
    encoder_driver.o(.text.get_right_motor_speed) refers to encoder_driver.o(.bss.get_right_motor_speed.cnt2) for [Anonymous Symbol]
    encoder_driver.o(.text.get_right_motor_speed) refers to encoder_driver.o(.data.NEncoder) for NEncoder
    encoder_driver.o(.text.get_right_motor_speed) refers to encoder_driver.o(.data.trackless_motor) for trackless_motor
    encoder_driver.o(.text.get_right_motor_speed) refers to encoder_driver.o(.bss.right_speed_filter_buf) for [Anonymous Symbol]
    encoder_driver.o(.text.get_right_motor_speed) refers to encoder_driver.o(.bss.speed_filter_param) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.get_right_motor_speed) refers to encoder_driver.o(.text.get_right_motor_speed) for [Anonymous Symbol]
    encoder_driver.o(.text.get_wheel_speed) refers to encoder_driver.o(.text.get_left_motor_speed) for get_left_motor_speed
    encoder_driver.o(.text.get_wheel_speed) refers to encoder_driver.o(.text.get_right_motor_speed) for get_right_motor_speed
    encoder_driver.o(.ARM.exidx.text.get_wheel_speed) refers to encoder_driver.o(.text.get_wheel_speed) for [Anonymous Symbol]
    interrupt_handler.o(.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.QEI1_IRQHandler) for QEI1_IRQHandler
    interrupt_handler.o(.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.QEI0_IRQHandler) for QEI0_IRQHandler
    interrupt_handler.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to interrupt_handler.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    ui.o(.text.Key_Scan) refers to nbutton.o(.text.read_button_state_all) for read_button_state_all
    ui.o(.text.Key_Scan) refers to nbutton.o(.text.get_key_short_press) for get_key_short_press
    ui.o(.text.Key_Scan) refers to oled.o(.text.LCD_CLS) for LCD_CLS
    ui.o(.text.Key_Scan) refers to ui.o(.bss.page_number) for page_number
    ui.o(.text.Key_Scan) refers to ui.o(.bss.display_counter) for [Anonymous Symbol]
    ui.o(.ARM.exidx.text.Key_Scan) refers to ui.o(.text.Key_Scan) for [Anonymous Symbol]
    ui.o(.text.screen_display) refers to nbutton.o(.text.read_button_state_all) for read_button_state_all
    ui.o(.text.screen_display) refers to nbutton.o(.text.get_key_short_press) for get_key_short_press
    ui.o(.text.screen_display) refers to oled.o(.text.LCD_CLS) for LCD_CLS
    ui.o(.text.screen_display) refers to ui.o(.bss.page_number) for page_number
    ui.o(.text.screen_display) refers to ui.o(.bss.display_counter) for [Anonymous Symbol]
    ui.o(.text.screen_display) refers to oled.o(.text.display_6_8_string) for display_6_8_string
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Is_Ready) for IMU_App_Is_Ready
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Angles) for IMU_App_Get_Angles
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Temperature) for IMU_App_Get_Temperature
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Calib_Status) for IMU_App_Get_Calib_Status
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Is_Temperature_Stable) for IMU_App_Is_Temperature_Stable
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_System_Status) for IMU_App_Get_System_Status
    ui.o(.text.screen_display) refers to oled.o(.text.LCD_clear_L) for LCD_clear_L
    ui.o(.text.screen_display) refers to ffixi.o(.text) for __aeabi_f2iz
    ui.o(.text.screen_display) refers to fflti.o(.text) for __aeabi_i2f
    ui.o(.text.screen_display) refers to oled.o(.text.display_6_8_number) for display_6_8_number
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Yaw_Quality) for IMU_App_Get_Yaw_Quality
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Fusion_Status) for IMU_App_Get_Fusion_Status
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Fusion_Timer) for IMU_App_Get_Fusion_Timer
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Gyro_Offset) for IMU_App_Get_Gyro_Offset
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Temperature_Range) for IMU_App_Get_Temperature_Range
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Calibration_Status) for IMU_App_Get_Calibration_Status
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Yaw_Drift_Rate) for IMU_App_Get_Yaw_Drift_Rate
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Yaw_Status_String) for IMU_App_Get_Yaw_Status_String
    ui.o(.text.screen_display) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Calibration_Status_String) for IMU_App_Get_Calibration_Status_String
    ui.o(.text.screen_display) refers to calibration.o(.text.Calibration_Get_CRC32) for Calibration_Get_CRC32
    ui.o(.text.screen_display) refers to calibration.o(.text.Calibration_Verify_Integrity) for Calibration_Verify_Integrity
    ui.o(.text.screen_display) refers to ui.o(.rodata.str1.1) for [Anonymous Symbol]
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Test_Results) for IMU_App_Get_Test_Results
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Test_Status_String) for IMU_App_Get_Test_Status_String
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Get_Performance_Stats) for IMU_App_Get_Performance_Stats
    ui.o(.text.screen_display) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    ui.o(.text.screen_display) refers to encoder_driver.o(.data.NEncoder) for NEncoder
    ui.o(.text.screen_display) refers to hui_app.o(.text.HUI_Read_All_Pins) for HUI_Read_All_Pins
    ui.o(.text.screen_display) refers to hui_app.o(.text.HUI_Get_Pin_State) for HUI_Get_Pin_State
    ui.o(.text.screen_display) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    ui.o(.text.screen_display) refers to imu_app.o(.text.IMU_App_Is_Stationary) for IMU_App_Is_Stationary
    ui.o(.ARM.exidx.text.screen_display) refers to ui.o(.text.screen_display) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Init) refers to icm20608.o(.text.ICM20608_Init) for ICM20608_Init
    imu_app.o(.text.IMU_App_Init) refers to system.o(.text.delay_ms) for delay_ms
    imu_app.o(.text.IMU_App_Init) refers to imu_app.o(.bss.imu_status) for imu_status
    imu_app.o(.text.IMU_App_Init) refers to imu_app.o(.bss.auto_calib_requested) for [Anonymous Symbol]
    imu_app.o(.ARM.exidx.text.IMU_App_Init) refers to imu_app.o(.text.IMU_App_Init) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Update) refers to icm20608.o(.text.ICM20608_Calibrate_Gyro) for ICM20608_Calibrate_Gyro
    imu_app.o(.text.IMU_App_Update) refers to icm20608.o(.text.ICM20608_Read_Data) for ICM20608_Read_Data
    imu_app.o(.text.IMU_App_Update) refers to icm20608.o(.text.ICM20608_Update_Angles) for ICM20608_Update_Angles
    imu_app.o(.text.IMU_App_Update) refers to icm20608.o(.text.ICM20608_Get_Angles) for ICM20608_Get_Angles
    imu_app.o(.text.IMU_App_Update) refers to imu_app.o(.bss.imu_status) for imu_status
    imu_app.o(.text.IMU_App_Update) refers to imu_app.o(.bss.auto_calib_requested) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Update) refers to imu_app.o(.bss.IMU_App_Update.retry_count) for [Anonymous Symbol]
    imu_app.o(.ARM.exidx.text.IMU_App_Update) refers to imu_app.o(.text.IMU_App_Update) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Angles) refers to imu_app.o(.bss.imu_status) for imu_status
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Angles) refers to imu_app.o(.text.IMU_App_Get_Angles) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Start_Calibration) refers to imu_app.o(.bss.auto_calib_requested) for [Anonymous Symbol]
    imu_app.o(.ARM.exidx.text.IMU_App_Start_Calibration) refers to imu_app.o(.text.IMU_App_Start_Calibration) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Calib_Status) refers to icm20608.o(.text.ICM20608_Get_Calib_Status) for ICM20608_Get_Calib_Status
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Calib_Status) refers to imu_app.o(.text.IMU_App_Get_Calib_Status) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Gyro_Offset) refers to icm20608.o(.text.ICM20608_Get_Gyro_Offset) for ICM20608_Get_Gyro_Offset
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Gyro_Offset) refers to imu_app.o(.text.IMU_App_Get_Gyro_Offset) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Fusion_Status) refers to icm20608.o(.text.ICM20608_Get_Fusion_Status) for ICM20608_Get_Fusion_Status
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Fusion_Status) refers to imu_app.o(.text.IMU_App_Get_Fusion_Status) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Fusion_Timer) refers to icm20608.o(.text.ICM20608_Get_Fusion_Timer) for ICM20608_Get_Fusion_Timer
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Fusion_Timer) refers to imu_app.o(.text.IMU_App_Get_Fusion_Timer) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Is_Stationary) refers to icm20608.o(.text.ICM20608_Get_Fusion_Timer) for ICM20608_Get_Fusion_Timer
    imu_app.o(.ARM.exidx.text.IMU_App_Is_Stationary) refers to imu_app.o(.text.IMU_App_Is_Stationary) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Is_Temperature_Stable) refers to icm20608.o(.text.ICM20608_Is_Temperature_Stable) for ICM20608_Is_Temperature_Stable
    imu_app.o(.ARM.exidx.text.IMU_App_Is_Temperature_Stable) refers to imu_app.o(.text.IMU_App_Is_Temperature_Stable) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Temperature_Range) refers to icm20608.o(.text.ICM20608_Get_Temperature_Range) for ICM20608_Get_Temperature_Range
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Temperature_Range) refers to imu_app.o(.text.IMU_App_Get_Temperature_Range) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_System_Status) refers to icm20608.o(.text.ICM20608_Is_Temperature_Stable) for ICM20608_Is_Temperature_Stable
    imu_app.o(.text.IMU_App_Get_System_Status) refers to imu_app.o(.bss.imu_status) for imu_status
    imu_app.o(.ARM.exidx.text.IMU_App_Get_System_Status) refers to imu_app.o(.text.IMU_App_Get_System_Status) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Yaw_Quality) refers to icm20608.o(.text.ICM20608_Get_Yaw_Quality) for ICM20608_Get_Yaw_Quality
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Yaw_Quality) refers to imu_app.o(.text.IMU_App_Get_Yaw_Quality) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Yaw_Drift_Rate) refers to icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate) for ICM20608_Get_Yaw_Drift_Rate
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Yaw_Drift_Rate) refers to imu_app.o(.text.IMU_App_Get_Yaw_Drift_Rate) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Reset_Yaw) refers to icm20608.o(.text.ICM20608_Reset_Yaw) for ICM20608_Reset_Yaw
    imu_app.o(.ARM.exidx.text.IMU_App_Reset_Yaw) refers to imu_app.o(.text.IMU_App_Reset_Yaw) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Yaw_Status_String) refers to icm20608.o(.text.ICM20608_Get_Yaw_Quality) for ICM20608_Get_Yaw_Quality
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Yaw_Status_String) refers to imu_app.o(.text.IMU_App_Get_Yaw_Status_String) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Save_Calibration) refers to icm20608.o(.text.ICM20608_Save_Calibration) for ICM20608_Save_Calibration
    imu_app.o(.ARM.exidx.text.IMU_App_Save_Calibration) refers to imu_app.o(.text.IMU_App_Save_Calibration) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Load_Calibration) refers to icm20608.o(.text.ICM20608_Load_Calibration) for ICM20608_Load_Calibration
    imu_app.o(.ARM.exidx.text.IMU_App_Load_Calibration) refers to imu_app.o(.text.IMU_App_Load_Calibration) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Reset_Calibration) refers to icm20608.o(.text.ICM20608_Reset_Calibration) for ICM20608_Reset_Calibration
    imu_app.o(.ARM.exidx.text.IMU_App_Reset_Calibration) refers to imu_app.o(.text.IMU_App_Reset_Calibration) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Calibration_Status) refers to icm20608.o(.text.ICM20608_Get_Calibration_Status) for ICM20608_Get_Calibration_Status
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Calibration_Status) refers to imu_app.o(.text.IMU_App_Get_Calibration_Status) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Calibration_Status_String) refers to icm20608.o(.text.ICM20608_Get_Calibration_Status) for ICM20608_Get_Calibration_Status
    imu_app.o(.text.IMU_App_Get_Calibration_Status_String) refers to imu_app.o(.rodata.str1.1) for [Anonymous Symbol]
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Calibration_Status_String) refers to imu_app.o(.text.IMU_App_Get_Calibration_Status_String) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Start_Drift_Test) refers to icm20608.o(.text.ICM20608_Reset_Yaw) for ICM20608_Reset_Yaw
    imu_app.o(.text.IMU_App_Start_Drift_Test) refers to system.o(.text.delay_ms) for delay_ms
    imu_app.o(.ARM.exidx.text.IMU_App_Start_Drift_Test) refers to imu_app.o(.text.IMU_App_Start_Drift_Test) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Test_Results) refers to icm20608.o(.text.ICM20608_Get_Yaw_Quality) for ICM20608_Get_Yaw_Quality
    imu_app.o(.text.IMU_App_Get_Test_Results) refers to icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate) for ICM20608_Get_Yaw_Drift_Rate
    imu_app.o(.text.IMU_App_Get_Test_Results) refers to icm20608.o(.text.ICM20608_Is_Temperature_Stable) for ICM20608_Is_Temperature_Stable
    imu_app.o(.text.IMU_App_Get_Test_Results) refers to icm20608.o(.text.ICM20608_Get_Fusion_Status) for ICM20608_Get_Fusion_Status
    imu_app.o(.text.IMU_App_Get_Test_Results) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    imu_app.o(.text.IMU_App_Get_Test_Results) refers to imu_app.o(.bss.imu_status) for imu_status
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Test_Results) refers to imu_app.o(.text.IMU_App_Get_Test_Results) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Performance_Stats) refers to icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate) for ICM20608_Get_Yaw_Drift_Rate
    imu_app.o(.text.IMU_App_Get_Performance_Stats) refers to icm20608.o(.text.ICM20608_Get_Yaw_Quality) for ICM20608_Get_Yaw_Quality
    imu_app.o(.text.IMU_App_Get_Performance_Stats) refers to icm20608.o(.text.ICM20608_Is_Temperature_Stable) for ICM20608_Is_Temperature_Stable
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Performance_Stats) refers to imu_app.o(.text.IMU_App_Get_Performance_Stats) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Test_Status_String) refers to icm20608.o(.text.ICM20608_Get_Yaw_Quality) for ICM20608_Get_Yaw_Quality
    imu_app.o(.text.IMU_App_Get_Test_Status_String) refers to icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate) for ICM20608_Get_Yaw_Drift_Rate
    imu_app.o(.text.IMU_App_Get_Test_Status_String) refers to icm20608.o(.text.ICM20608_Is_Temperature_Stable) for ICM20608_Is_Temperature_Stable
    imu_app.o(.text.IMU_App_Get_Test_Status_String) refers to icm20608.o(.text.ICM20608_Get_Fusion_Status) for ICM20608_Get_Fusion_Status
    imu_app.o(.text.IMU_App_Get_Test_Status_String) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    imu_app.o(.text.IMU_App_Get_Test_Status_String) refers to imu_app.o(.bss.imu_status) for imu_status
    imu_app.o(.text.IMU_App_Get_Test_Status_String) refers to imu_app.o(.rodata.str1.1) for [Anonymous Symbol]
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Test_Status_String) refers to imu_app.o(.text.IMU_App_Get_Test_Status_String) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Get_Temperature) refers to imu_app.o(.bss.imu_status) for imu_status
    imu_app.o(.ARM.exidx.text.IMU_App_Get_Temperature) refers to imu_app.o(.text.IMU_App_Get_Temperature) for [Anonymous Symbol]
    imu_app.o(.text.IMU_App_Is_Ready) refers to imu_app.o(.bss.imu_status) for imu_status
    imu_app.o(.ARM.exidx.text.IMU_App_Is_Ready) refers to imu_app.o(.text.IMU_App_Is_Ready) for [Anonymous Symbol]
    filter.o(.text.LPButterworth) refers to fcmp.o(i._feq) for __aeabi_fcmpeq
    filter.o(.text.LPButterworth) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    filter.o(.text.LPButterworth) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    filter.o(.text.LPButterworth) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    filter.o(.ARM.exidx.text.LPButterworth) refers to filter.o(.text.LPButterworth) for [Anonymous Symbol]
    filter.o(.text.set_cutoff_frequency) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    filter.o(.text.set_cutoff_frequency) refers to fdiv.o(.text) for __aeabi_fdiv
    filter.o(.text.set_cutoff_frequency) refers to tanf.o(i.tanf) for tanf
    filter.o(.text.set_cutoff_frequency) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    filter.o(.text.set_cutoff_frequency) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    filter.o(.ARM.exidx.text.set_cutoff_frequency) refers to filter.o(.text.set_cutoff_frequency) for [Anonymous Symbol]
    fusionoffset.o(.text.FusionOffsetInitialise) refers to fflti.o(.text) for __aeabi_ui2f
    fusionoffset.o(.text.FusionOffsetInitialise) refers to fdiv.o(.text) for __aeabi_fdiv
    fusionoffset.o(.text.FusionOffsetInitialise) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    fusionoffset.o(.ARM.exidx.text.FusionOffsetInitialise) refers to fusionoffset.o(.text.FusionOffsetInitialise) for [Anonymous Symbol]
    fusionoffset.o(.text.FusionOffsetUpdate) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    fusionoffset.o(.text.FusionOffsetUpdate) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    fusionoffset.o(.text.FusionOffsetUpdate) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    fusionoffset.o(.text.FusionOffsetUpdate) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    fusionoffset.o(.ARM.exidx.text.FusionOffsetUpdate) refers to fusionoffset.o(.text.FusionOffsetUpdate) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Init) refers to rt_memclr.o(.text) for __aeabi_memclr4
    calibration.o(.text.Calibration_Init) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Init) refers to calibration.o(.bss.save_timer) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Init) refers to calibration.o(.bss.data_changed) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Init) refers to calibration.o(.rodata.crc32_table) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Init) refers to calibration.o(.bss.calib_status) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Init) refers to calibration.o(.text.Calibration_Init) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Load) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Load) refers to calibration.o(.rodata.crc32_table) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Load) refers to calibration.o(.bss.calib_status) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Load) refers to calibration.o(.text.Calibration_Load) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Save) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Save) refers to calibration.o(.rodata.crc32_table) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Save) refers to calibration.o(.bss.calib_status) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Save) refers to calibration.o(.bss.data_changed) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Save) refers to calibration.o(.text.Calibration_Save) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Reset) refers to rt_memclr.o(.text) for __aeabi_memclr4
    calibration.o(.text.Calibration_Reset) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Reset) refers to calibration.o(.bss.save_timer) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Reset) refers to calibration.o(.rodata.crc32_table) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Reset) refers to calibration.o(.bss.calib_status) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Reset) refers to calibration.o(.bss.data_changed) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Reset) refers to calibration.o(.text.Calibration_Reset) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Get_Status) refers to calibration.o(.bss.calib_status) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Get_Status) refers to calibration.o(.text.Calibration_Get_Status) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Set_Gyro_Offset) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Set_Gyro_Offset) refers to calibration.o(.bss.data_changed) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Set_Gyro_Offset) refers to calibration.o(.text.Calibration_Set_Gyro_Offset) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Get_Gyro_Offset) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Get_Gyro_Offset) refers to calibration.o(.text.Calibration_Get_Gyro_Offset) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Set_Accel_Offset) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Set_Accel_Offset) refers to calibration.o(.bss.data_changed) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Set_Accel_Offset) refers to calibration.o(.text.Calibration_Set_Accel_Offset) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Get_Accel_Offset) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Get_Accel_Offset) refers to calibration.o(.text.Calibration_Get_Accel_Offset) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Set_Temp_Coeff) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Set_Temp_Coeff) refers to calibration.o(.bss.data_changed) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Set_Temp_Coeff) refers to calibration.o(.text.Calibration_Set_Temp_Coeff) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Get_Temp_Coeff) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Get_Temp_Coeff) refers to calibration.o(.text.Calibration_Get_Temp_Coeff) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Enable_Auto_Save) refers to calibration.o(.data.auto_save_enabled) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Enable_Auto_Save) refers to calibration.o(.text.Calibration_Enable_Auto_Save) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Update) refers to calibration.o(.bss.data_changed) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Update) refers to calibration.o(.data.auto_save_enabled) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Update) refers to calibration.o(.bss.save_timer) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Update) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Update) refers to calibration.o(.rodata.crc32_table) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Update) refers to calibration.o(.bss.calib_status) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Update) refers to calibration.o(.text.Calibration_Update) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Get_CRC32) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Get_CRC32) refers to calibration.o(.rodata.crc32_table) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Get_CRC32) refers to calibration.o(.text.Calibration_Get_CRC32) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Verify_Integrity) refers to calibration.o(.bss.calib_data) for [Anonymous Symbol]
    calibration.o(.text.Calibration_Verify_Integrity) refers to calibration.o(.rodata.crc32_table) for [Anonymous Symbol]
    calibration.o(.ARM.exidx.text.Calibration_Verify_Integrity) refers to calibration.o(.text.Calibration_Verify_Integrity) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_init) refers to pid.o(.text.pid_init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_target) refers to pid.o(.text.pid_set_target) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_params) refers to pid.o(.text.pid_set_params) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_limit) refers to pid.o(.text.pid_set_limit) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_reset) refers to pid.o(.text.pid_reset) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_positional) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(.text.pid_calculate_positional) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(.text.pid_calculate_positional) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(.text.pid_calculate_positional) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    pid.o(.text.pid_calculate_positional) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_calculate_positional) refers to pid.o(.text.pid_calculate_positional) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_incremental) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(.text.pid_calculate_incremental) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(.text.pid_calculate_incremental) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(.text.pid_calculate_incremental) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    pid.o(.text.pid_calculate_incremental) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_calculate_incremental) refers to pid.o(.text.pid_calculate_incremental) for [Anonymous Symbol]
    pid.o(.text.pid_constrain) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    pid.o(.text.pid_constrain) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    pid.o(.ARM.exidx.text.pid_constrain) refers to pid.o(.text.pid_constrain) for [Anonymous Symbol]
    pid.o(.text.pid_app_limit_integral) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    pid.o(.text.pid_app_limit_integral) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_app_limit_integral) refers to pid.o(.text.pid_app_limit_integral) for [Anonymous Symbol]
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy.o(.text) refers to rt_memcpy.o(.emb_text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    faddsub.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fadd) for _fadd1
    faddsub.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fsub) refers to faddsub.o(x$fpl$fadd) for _fadd1
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fdiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(.text) refers to fdiv.o(.constdata) for .constdata
    fdiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixui.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.atan2f) for atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to fdiv.o(.text) for __aeabi_fdiv
    atan2f.o(i.atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.atan2f) refers to _rserrno.o(.text) for __set_errno
    atan2f.o(i.atan2f) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    atan2f.o(i.atan2f) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    atan2f.o(i.atan2f) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f.o(i.atan2f) refers to faddsub.o(x$fpl$frsb) for __aeabi_frsub
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to fsqrt.o(.text) for _fsqrt
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to fsqrt.o(.text) for _fsqrt
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    tanf.o(i.__softfp_tanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf.o(i.__softfp_tanf) refers to tanf.o(i.tanf) for tanf
    tanf.o(i.tanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf.o(i.tanf) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    tanf.o(i.tanf) refers to frnd.o(.text) for _frnd
    tanf.o(i.tanf) refers to ffixi.o(.text) for __aeabi_f2iz
    tanf.o(i.tanf) refers to faddsub.o(x$fpl$frsb) for __aeabi_frsub
    tanf.o(i.tanf) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    tanf.o(i.tanf) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    tanf.o(i.tanf) refers to fdiv.o(.text) for __aeabi_fdiv
    tanf.o(i.tanf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    tanf.o(i.tanf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    tanf.o(i.tanf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    tanf.o(i.tanf) refers to _rserrno.o(.text) for __set_errno
    tanf.o(i.tanf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    tanf.o(i.tanf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    frnd.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fsqrt.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    funder.o(i.__mathlib_flt_divzero) refers to fdiv.o(.text) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_infnan) refers to fscalbn.o(.text) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_infnan2) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    funder.o(i.__mathlib_flt_invalid) refers to fdiv.o(.text) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_overflow) refers to fscalbn.o(.text) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_posinfnan) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    funder.o(i.__mathlib_flt_underflow) refers to fscalbn.o(.text) for __ARM_scalbnf
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    rredf.o(i.__mathlib_rredf2) refers to fflti.o(.text) for __aeabi_i2f
    rredf.o(i.__mathlib_rredf2) refers to fscalbn.o(.text) for __ARM_scalbnf
    rredf.o(i.__mathlib_rredf2) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    rredf.o(i.__mathlib_rredf2) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    rredf.o(i.__mathlib_rredf2) refers to faddsub.o(x$fpl$frsb) for __aeabi_frsub
    rredf.o(i.__mathlib_rredf2) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    fscalbn.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    rredf.o(i.__ARM_common_ll_muluu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_aes.o(.text), (0 bytes).
    Removing dl_aes.o(.text.DL_AES_setKey), (72 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKey), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_setKeyAligned), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOut), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOutAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorData), (52 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorData), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorDataAligned), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_saveConfiguration), (60 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_restoreConfiguration), (60 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration), (8 bytes).
    Removing dl_aesadv.o(.text), (0 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_crc.o(.text), (0 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock32), (92 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange32), (52 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock16), (152 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange16), (104 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16), (8 bytes).
    Removing dl_crcp.o(.text), (0 bytes).
    Removing dl_dac12.o(.text), (0 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_init), (136 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_init), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking8), (48 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO8), (160 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking), (36 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_flashctl.o(.text), (0 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemory), (22 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.ramfunc), (64 bytes).
    Removing dl_flashctl.o(.ARM.exidx.ramfunc), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massErase), (232 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory), (16 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank), (152 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM), (328 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM), (116 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank), (408 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryReset), (92 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory), (12 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank), (92 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (136 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectSector), (224 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (100 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (168 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (120 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking), (160 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM), (120 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectSector), (240 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerify), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (132 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_keystorectl.o(.text), (0 bytes).
    Removing dl_lcd.o(.text), (0 bytes).
    Removing dl_lfss.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_configOperation), (40 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setClockConfig), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isMemInitDone), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setOpMode), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getOpMode), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_init), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (212 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setBitTime), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_msgRAMConfig), (536 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setExtIDAndMask), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (264 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (292 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_selectIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (220 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (128 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (112 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (56 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRevisionId), (68 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (228 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (276 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_opa.o(.text), (0 bytes).
    Removing dl_opa.o(.text.DL_OPA_increaseGain), (52 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_decreaseGain), (48 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain), (8 bytes).
    Removing dl_rtc_common.o(.text), (0 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_initCalendar), (144 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime), (124 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1), (96 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1), (84 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1), (60 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2), (96 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2), (84 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2), (60 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (116 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (116 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (80 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (112 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (268 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (228 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (172 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (96 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (192 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (196 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_trng.o(.text), (0 bytes).
    Removing dl_trng.o(.text.DL_TRNG_saveConfiguration), (44 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_restoreConfiguration), (72 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (68 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (48 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_vref.o(.text), (0 bytes).
    Removing dl_vref.o(.text.DL_VREF_configReference), (32 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_configReference), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_setClockConfig), (18 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_getClockConfig), (16 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig), (8 bytes).
    Removing dl_interrupt.o(.text), (0 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_registerInterrupt), (144 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt), (8 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt), (20 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt), (8 bytes).
    Removing dl_interrupt.o(.vtable), (192 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (100 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (96 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_2_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_G0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_G6_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_G8_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_G12_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_3_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (120 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (132 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init), (8 bytes).
    Removing led_app.o(.text), (0 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_App_Init), (8 bytes).
    Removing led_app.o(.text.RGB_All_Off), (24 bytes).
    Removing led_app.o(.ARM.exidx.text.RGB_All_Off), (8 bytes).
    Removing led_app.o(.text.LED_Red_Control), (24 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Red_Control), (8 bytes).
    Removing led_app.o(.text.LED_Green_Control), (24 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Green_Control), (8 bytes).
    Removing led_app.o(.text.LED_Blue_Control), (24 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Blue_Control), (8 bytes).
    Removing led_app.o(.text.LED_Red_On), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Red_On), (8 bytes).
    Removing led_app.o(.text.LED_Red_Off), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Red_Off), (8 bytes).
    Removing led_app.o(.text.LED_Green_On), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Green_On), (8 bytes).
    Removing led_app.o(.text.LED_Green_Off), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Green_Off), (8 bytes).
    Removing led_app.o(.text.LED_Blue_On), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Blue_On), (8 bytes).
    Removing led_app.o(.text.LED_Blue_Off), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Blue_Off), (8 bytes).
    Removing led_app.o(.text.LED_Red_Toggle), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Red_Toggle), (8 bytes).
    Removing led_app.o(.text.LED_Green_Toggle), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Green_Toggle), (8 bytes).
    Removing led_app.o(.text.LED_Blue_Toggle), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Blue_Toggle), (8 bytes).
    Removing led_app.o(.text.RGB_Set_Color), (52 bytes).
    Removing led_app.o(.ARM.exidx.text.RGB_Set_Color), (8 bytes).
    Removing led_app.o(.text.RGB_All_On), (24 bytes).
    Removing led_app.o(.ARM.exidx.text.RGB_All_On), (8 bytes).
    Removing led_app.o(.text.LED_Red_Get_State), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Red_Get_State), (8 bytes).
    Removing led_app.o(.text.LED_Green_Get_State), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Green_Get_State), (8 bytes).
    Removing led_app.o(.text.LED_Blue_Get_State), (16 bytes).
    Removing led_app.o(.ARM.exidx.text.LED_Blue_Get_State), (8 bytes).
    Removing led_app.o(.text.RGB_Get_Color), (36 bytes).
    Removing led_app.o(.ARM.exidx.text.RGB_Get_Color), (8 bytes).
    Removing led_app.o(.text.RGB_Test_Sequence), (164 bytes).
    Removing led_app.o(.ARM.exidx.text.RGB_Test_Sequence), (8 bytes).
    Removing led_app.o(.text.RGB_Breathing_Effect), (96 bytes).
    Removing led_app.o(.ARM.exidx.text.RGB_Breathing_Effect), (8 bytes).
    Removing nbutton.o(.text), (0 bytes).
    Removing nbutton.o(.ARM.exidx.text.Button_Init), (8 bytes).
    Removing nbutton.o(.ARM.exidx.text.Read_Button_State_One), (8 bytes).
    Removing nbutton.o(.ARM.exidx.text.read_button_state_all), (8 bytes).
    Removing nbutton.o(.text.get_key_val), (68 bytes).
    Removing nbutton.o(.ARM.exidx.text.get_key_val), (8 bytes).
    Removing nbutton.o(.ARM.exidx.text.get_key_short_press), (8 bytes).
    Removing nbutton.o(.text.get_key_long_press), (84 bytes).
    Removing nbutton.o(.ARM.exidx.text.get_key_long_press), (8 bytes).
    Removing nbutton.o(.text.key_process), (316 bytes).
    Removing nbutton.o(.ARM.exidx.text.key_process), (8 bytes).
    Removing buzzer_app.o(.text), (0 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Init), (8 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Work), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_On), (20 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_On), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Off), (24 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Off), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Setup), (28 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Setup), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Tone), (28 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Tone), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Mode), (88 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Mode), (8 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Beep), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Double_Beep), (28 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Double_Beep), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Triple_Beep), (28 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Triple_Beep), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Long_Beep), (28 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Long_Beep), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Alarm), (28 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Alarm), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Success), (28 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Success), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Error), (28 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Error), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Key_Press), (24 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Key_Press), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Is_Working), (16 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Is_Working), (8 bytes).
    Removing buzzer_app.o(.text.Buzzer_Stop), (28 bytes).
    Removing buzzer_app.o(.ARM.exidx.text.Buzzer_Stop), (8 bytes).
    Removing uart_app.o(.text), (0 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_Init), (8 bytes).
    Removing uart_app.o(.text.UART0_App_SendByte), (16 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_SendByte), (8 bytes).
    Removing uart_app.o(.text.UART0_App_SendString), (36 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_SendString), (8 bytes).
    Removing uart_app.o(.text.UART0_App_SendNumber), (60 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_SendNumber), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_SendBytes), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_RxProcess), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_GetRxData), (8 bytes).
    Removing uart_app.o(.text.UART0_App_ClearRxBuffer), (36 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_ClearRxBuffer), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_IsRxComplete), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.UART0_App_RxCallback), (8 bytes).
    Removing uart_process.o(.text), (0 bytes).
    Removing uart_process.o(.ARM.exidx.text.uart_process), (8 bytes).
    Removing motor_app.o(.text), (0 bytes).
    Removing motor_app.o(.ARM.exidx.text.Motor_App_Init), (8 bytes).
    Removing motor_app.o(.text.car_stop), (52 bytes).
    Removing motor_app.o(.ARM.exidx.text.car_stop), (8 bytes).
    Removing motor_app.o(.text.left_wheel_forward), (48 bytes).
    Removing motor_app.o(.ARM.exidx.text.left_wheel_forward), (8 bytes).
    Removing motor_app.o(.text.left_wheel_backward), (48 bytes).
    Removing motor_app.o(.ARM.exidx.text.left_wheel_backward), (8 bytes).
    Removing motor_app.o(.text.right_wheel_forward), (52 bytes).
    Removing motor_app.o(.ARM.exidx.text.right_wheel_forward), (8 bytes).
    Removing motor_app.o(.text.right_wheel_backward), (48 bytes).
    Removing motor_app.o(.ARM.exidx.text.right_wheel_backward), (8 bytes).
    Removing motor_app.o(.text.car_move), (180 bytes).
    Removing motor_app.o(.ARM.exidx.text.car_move), (8 bytes).
    Removing motor_app.o(.text.motor_demo), (760 bytes).
    Removing motor_app.o(.ARM.exidx.text.motor_demo), (8 bytes).
    Removing hui_app.o(.text), (0 bytes).
    Removing hui_app.o(.text.HUI_App_Init), (124 bytes).
    Removing hui_app.o(.ARM.exidx.text.HUI_App_Init), (8 bytes).
    Removing hui_app.o(.ARM.exidx.text.HUI_Read_All_Pins), (8 bytes).
    Removing hui_app.o(.ARM.exidx.text.HUI_Get_Pin_State), (8 bytes).
    Removing system.o(.text), (0 bytes).
    Removing system.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing system.o(.ARM.exidx.text.ncontroller_set_priority), (8 bytes).
    Removing system.o(.text.micros), (44 bytes).
    Removing system.o(.ARM.exidx.text.micros), (8 bytes).
    Removing system.o(.ARM.exidx.text.millis), (8 bytes).
    Removing system.o(.text.delayMicroseconds), (188 bytes).
    Removing system.o(.ARM.exidx.text.delayMicroseconds), (8 bytes).
    Removing system.o(.text.delay), (188 bytes).
    Removing system.o(.ARM.exidx.text.delay), (8 bytes).
    Removing system.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing system.o(.text.delay_us), (188 bytes).
    Removing system.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing system.o(.ARM.exidx.text.Delay_Ms), (8 bytes).
    Removing system.o(.text.Delay_Us), (188 bytes).
    Removing system.o(.ARM.exidx.text.Delay_Us), (8 bytes).
    Removing system.o(.ARM.exidx.text.get_systime), (8 bytes).
    Removing system.o(.text.get_systime_ms), (52 bytes).
    Removing system.o(.ARM.exidx.text.get_systime_ms), (8 bytes).
    Removing system.o(.text.get_systick_ms), (12 bytes).
    Removing system.o(.ARM.exidx.text.get_systick_ms), (8 bytes).
    Removing system.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing system.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WrDat), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WrCmd), (8 bytes).
    Removing oled.o(.text.OLED_Set_Pos), (32 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing oled.o(.text.OLED_Fill), (180 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Fill), (8 bytes).
    Removing oled.o(.text.OLED_CLS), (372 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_CLS), (8 bytes).
    Removing oled.o(.text.OLED_Init_I2C), (1660 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init_I2C), (8 bytes).
    Removing oled.o(.text.LCD_Set_Pos), (32 bytes).
    Removing oled.o(.ARM.exidx.text.LCD_Set_Pos), (8 bytes).
    Removing oled.o(.text.LCD_Fill), (180 bytes).
    Removing oled.o(.ARM.exidx.text.LCD_Fill), (8 bytes).
    Removing oled.o(.ARM.exidx.text.LCD_CLS), (8 bytes).
    Removing oled.o(.ARM.exidx.text.LCD_P6x8Str), (8 bytes).
    Removing oled.o(.ARM.exidx.text.LCD_P6x8Char), (8 bytes).
    Removing oled.o(.ARM.exidx.text.write_6_8_number), (8 bytes).
    Removing oled.o(.text.write_6_8_number_f1), (668 bytes).
    Removing oled.o(.ARM.exidx.text.write_6_8_number_f1), (8 bytes).
    Removing oled.o(.text.LCD_P8x16Str), (232 bytes).
    Removing oled.o(.ARM.exidx.text.LCD_P8x16Str), (8 bytes).
    Removing oled.o(.text.LCD_P8x16Char), (188 bytes).
    Removing oled.o(.ARM.exidx.text.LCD_P8x16Char), (8 bytes).
    Removing oled.o(.text.write_8_16_number), (352 bytes).
    Removing oled.o(.ARM.exidx.text.write_8_16_number), (8 bytes).
    Removing oled.o(.text.write_16_16_CN), (60 bytes).
    Removing oled.o(.ARM.exidx.text.write_16_16_CN), (8 bytes).
    Removing oled.o(.ARM.exidx.text.LCD_clear_L), (8 bytes).
    Removing oled.o(.ARM.exidx.text.Draw_Logo), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.display_6_8_number), (8 bytes).
    Removing oled.o(.text.display_6_8_number_pro), (54 bytes).
    Removing oled.o(.ARM.exidx.text.display_6_8_number_pro), (8 bytes).
    Removing oled.o(.ARM.exidx.text.display_6_8_string), (8 bytes).
    Removing oled.o(.rodata.F8X16), (1520 bytes).
    Removing oled.o(.rodata.asc2_1608), (1520 bytes).
    Removing ssd1306.o(.text), (0 bytes).
    Removing ssd1306.o(.text.ssd1306_width), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_width), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_height), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_height), (8 bytes).
    Removing ssd1306.o(.text.set_rotation), (80 bytes).
    Removing ssd1306.o(.ARM.exidx.text.set_rotation), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_command), (8 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_command), (8 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_begin), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_pixel), (240 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_pixel), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_invert_display), (22 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_invert_display), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_start_scroll_right), (58 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_right), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_start_scroll_left), (58 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_left), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_start_scroll_diag_right), (70 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_diag_right), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_start_scroll_diag_left), (70 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_diag_left), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_stop_scroll), (10 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_stop_scroll), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_dim), (44 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_dim), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_data), (8 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_data), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_display), (46 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_display), (8 bytes).
    Removing ssd1306.o(.text.draw_oled), (244 bytes).
    Removing ssd1306.o(.ARM.exidx.text.draw_oled), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_clear_display), (20 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_clear_display), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_fast_hline), (488 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_hline), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_fast_vline_internal), (504 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_vline_internal), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_fast_hline_internal), (392 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_hline_internal), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_fast_vline), (484 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_vline), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_circle), (280 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_circle), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_circle_helper), (314 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_circle_helper), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_circle), (50 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_circle), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_circle_helper), (230 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_circle_helper), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_line), (182 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_line), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_rect), (70 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_rect), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_rect), (46 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_rect), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_screen), (56 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_screen), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_round_rect), (552 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_round_rect), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_round_rect), (342 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_round_rect), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_triangle), (56 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_triangle), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_triangle), (412 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_triangle), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_bitmap), (120 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_bitmap), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_bitmap_bg), (130 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_bitmap_bg), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_xbitmap), (120 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_xbitmap), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_write), (144 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_write), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_char), (296 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_char), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_cursor), (20 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_cursor), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_get_cursor_x), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_get_cursor_x), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_get_cursor_y), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_get_cursor_y), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_textsize), (16 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_textsize), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_textcolor), (20 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_textcolor), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_textcolor_bg), (20 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_textcolor_bg), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_textwrap), (16 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_textwrap), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_get_rotation), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_get_rotation), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_rotation), (80 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_rotation), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_cp437), (16 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_cp437), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_putstring), (160 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_putstring), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_puts), (180 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_puts), (8 bytes).
    Removing ssd1306.o(.rodata.font), (1275 bytes).
    Removing ssd1306.o(.data.buffer), (1024 bytes).
    Removing ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation), (16 bytes).
    Removing ssd1306.o(.rodata.cst8), (16 bytes).
    Removing ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6), (16 bytes).
    Removing ntimer.o(.text), (0 bytes).
    Removing ntimer.o(.ARM.exidx.text.timer_irq_config), (8 bytes).
    Removing ntimer.o(.text.timer_pwm_config), (40 bytes).
    Removing ntimer.o(.ARM.exidx.text.timer_pwm_config), (8 bytes).
    Removing ntimer.o(.ARM.exidx.text.TIMG0_IRQHandler), (8 bytes).
    Removing ntimer.o(.ARM.exidx.text.TIMG6_IRQHandler), (8 bytes).
    Removing ntimer.o(.ARM.exidx.text.TIMG8_IRQHandler), (8 bytes).
    Removing ntimer.o(.ARM.exidx.text.TIMG12_IRQHandler), (8 bytes).
    Removing ntimer.o(.text.Reserved_PWM5_Output), (20 bytes).
    Removing ntimer.o(.ARM.exidx.text.Reserved_PWM5_Output), (8 bytes).
    Removing ntimer.o(.text.Reserved_PWM6_Output), (20 bytes).
    Removing ntimer.o(.ARM.exidx.text.Reserved_PWM6_Output), (8 bytes).
    Removing ntimer.o(.text.Reserved_PWM7_Output), (20 bytes).
    Removing ntimer.o(.ARM.exidx.text.Reserved_PWM7_Output), (8 bytes).
    Removing ntimer.o(.text.Reserved_PWM8_Output), (20 bytes).
    Removing ntimer.o(.ARM.exidx.text.Reserved_PWM8_Output), (8 bytes).
    Removing ntimer.o(.bss.timer_t1a), (16 bytes).
    Removing ntimer.o(.bss.timer_t5a), (16 bytes).
    Removing ntimer.o(.bss.timer_t6a), (16 bytes).
    Removing ntimer.o(.bss.timer_t7a), (16 bytes).
    Removing nuart.o(.text), (0 bytes).
    Removing nuart.o(.ARM.exidx.text.usart_irq_config), (8 bytes).
    Removing nuart.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing nuart.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing nuart.o(.ARM.exidx.text.UART2_IRQHandler), (8 bytes).
    Removing nuart.o(.ARM.exidx.text.UART3_IRQHandler), (8 bytes).
    Removing nuart.o(.text.usart0_send_string), (40 bytes).
    Removing nuart.o(.ARM.exidx.text.usart0_send_string), (8 bytes).
    Removing nuart.o(.text.usart0_send_bytes), (32 bytes).
    Removing nuart.o(.ARM.exidx.text.usart0_send_bytes), (8 bytes).
    Removing nuart.o(.text.fputc), (20 bytes).
    Removing nuart.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing nuart.o(.text.usart1_send_bytes), (32 bytes).
    Removing nuart.o(.ARM.exidx.text.usart1_send_bytes), (8 bytes).
    Removing nuart.o(.text.UART_SendBytes), (28 bytes).
    Removing nuart.o(.ARM.exidx.text.UART_SendBytes), (8 bytes).
    Removing nuart.o(.text.UART_SendByte), (8 bytes).
    Removing nuart.o(.ARM.exidx.text.UART_SendByte), (8 bytes).
    Removing ni2c.o(.text), (0 bytes).
    Removing ni2c.o(.text.I2C_WriteReg), (236 bytes).
    Removing ni2c.o(.ARM.exidx.text.I2C_WriteReg), (8 bytes).
    Removing ni2c.o(.ARM.exidx.text.I2C_ReadReg), (8 bytes).
    Removing ni2c.o(.text.single_writei2c), (220 bytes).
    Removing ni2c.o(.ARM.exidx.text.single_writei2c), (8 bytes).
    Removing ni2c.o(.text.single_readi2c), (24 bytes).
    Removing ni2c.o(.ARM.exidx.text.single_readi2c), (8 bytes).
    Removing ni2c.o(.ARM.exidx.text.i2creadnbyte), (8 bytes).
    Removing icm20608.o(.text), (0 bytes).
    Removing icm20608.o(.text.ICM20608_Init), (444 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Init), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Calibrate_Gyro), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Get_Calib_Status), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Get_Gyro_Offset), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Get_Fusion_Status), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Get_Fusion_Timer), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Is_Temperature_Stable), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Get_Temperature_Range), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Get_Yaw_Quality), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Get_Yaw_Drift_Rate), (8 bytes).
    Removing icm20608.o(.text.ICM20608_Reset_Yaw), (12 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Reset_Yaw), (8 bytes).
    Removing icm20608.o(.text.ICM20608_Save_Calibration), (32 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Save_Calibration), (8 bytes).
    Removing icm20608.o(.text.ICM20608_Load_Calibration), (64 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Load_Calibration), (8 bytes).
    Removing icm20608.o(.text.ICM20608_Reset_Calibration), (32 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Reset_Calibration), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Get_Calibration_Status), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Read_Data), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Update_Angles), (8 bytes).
    Removing icm20608.o(.ARM.exidx.text.ICM20608_Get_Angles), (8 bytes).
    Removing encoder_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.Encoder_Init), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.QEI0_IRQHandler), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.QEI1_IRQHandler), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.get_left_motor_speed), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.get_right_motor_speed), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.get_wheel_speed), (8 bytes).
    Removing interrupt_handler.o(.text), (0 bytes).
    Removing interrupt_handler.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing ui.o(.text), (0 bytes).
    Removing ui.o(.text.Key_Scan), (76 bytes).
    Removing ui.o(.ARM.exidx.text.Key_Scan), (8 bytes).
    Removing ui.o(.ARM.exidx.text.screen_display), (8 bytes).
    Removing imu_app.o(.text), (0 bytes).
    Removing imu_app.o(.text.IMU_App_Init), (68 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Init), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Update), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Angles), (8 bytes).
    Removing imu_app.o(.text.IMU_App_Start_Calibration), (12 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Start_Calibration), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Calib_Status), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Gyro_Offset), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Fusion_Status), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Fusion_Timer), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Is_Stationary), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Is_Temperature_Stable), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Temperature_Range), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_System_Status), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Yaw_Quality), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Yaw_Drift_Rate), (8 bytes).
    Removing imu_app.o(.text.IMU_App_Reset_Yaw), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Reset_Yaw), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Yaw_Status_String), (8 bytes).
    Removing imu_app.o(.text.IMU_App_Save_Calibration), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Save_Calibration), (8 bytes).
    Removing imu_app.o(.text.IMU_App_Load_Calibration), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Load_Calibration), (8 bytes).
    Removing imu_app.o(.text.IMU_App_Reset_Calibration), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Reset_Calibration), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Calibration_Status), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Calibration_Status_String), (8 bytes).
    Removing imu_app.o(.text.IMU_App_Start_Drift_Test), (16 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Start_Drift_Test), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Test_Results), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Performance_Stats), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Test_Status_String), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Get_Temperature), (8 bytes).
    Removing imu_app.o(.ARM.exidx.text.IMU_App_Is_Ready), (8 bytes).
    Removing filter.o(.text), (0 bytes).
    Removing filter.o(.ARM.exidx.text.LPButterworth), (8 bytes).
    Removing filter.o(.ARM.exidx.text.set_cutoff_frequency), (8 bytes).
    Removing fusionoffset.o(.text), (0 bytes).
    Removing fusionoffset.o(.text.FusionOffsetInitialise), (52 bytes).
    Removing fusionoffset.o(.ARM.exidx.text.FusionOffsetInitialise), (8 bytes).
    Removing fusionoffset.o(.ARM.exidx.text.FusionOffsetUpdate), (8 bytes).
    Removing calibration.o(.text), (0 bytes).
    Removing calibration.o(.text.Calibration_Init), (336 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Init), (8 bytes).
    Removing calibration.o(.text.Calibration_Load), (308 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Load), (8 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Save), (8 bytes).
    Removing calibration.o(.text.Calibration_Reset), (340 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Reset), (8 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Get_Status), (8 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Set_Gyro_Offset), (8 bytes).
    Removing calibration.o(.text.Calibration_Get_Gyro_Offset), (20 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Get_Gyro_Offset), (8 bytes).
    Removing calibration.o(.text.Calibration_Set_Accel_Offset), (24 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Set_Accel_Offset), (8 bytes).
    Removing calibration.o(.text.Calibration_Get_Accel_Offset), (20 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Get_Accel_Offset), (8 bytes).
    Removing calibration.o(.text.Calibration_Set_Temp_Coeff), (44 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Set_Temp_Coeff), (8 bytes).
    Removing calibration.o(.text.Calibration_Get_Temp_Coeff), (32 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Get_Temp_Coeff), (8 bytes).
    Removing calibration.o(.text.Calibration_Enable_Auto_Save), (12 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Enable_Auto_Save), (8 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Update), (8 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Get_CRC32), (8 bytes).
    Removing calibration.o(.ARM.exidx.text.Calibration_Verify_Integrity), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.text.pid_init), (28 bytes).
    Removing pid.o(.ARM.exidx.text.pid_init), (8 bytes).
    Removing pid.o(.text.pid_set_target), (20 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_target), (8 bytes).
    Removing pid.o(.text.pid_set_params), (4 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_params), (8 bytes).
    Removing pid.o(.text.pid_set_limit), (4 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_limit), (8 bytes).
    Removing pid.o(.text.pid_reset), (18 bytes).
    Removing pid.o(.ARM.exidx.text.pid_reset), (8 bytes).
    Removing pid.o(.text.pid_calculate_positional), (138 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_positional), (8 bytes).
    Removing pid.o(.text.pid_calculate_incremental), (158 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_incremental), (8 bytes).
    Removing pid.o(.text.pid_constrain), (42 bytes).
    Removing pid.o(.ARM.exidx.text.pid_constrain), (8 bytes).
    Removing pid.o(.text.pid_app_limit_integral), (44 bytes).
    Removing pid.o(.ARM.exidx.text.pid_app_limit_integral), (8 bytes).

1111 unused section(s) (total 47077 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/fdiv.c                   0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/frnd.c                   0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/cfplib/fsqrt.c                  0x00000000   Number         0  fsqrt.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/cfplib/scalbn.c                 0x00000000   Number         0  fscalbn.o ABSOLUTE
    ../fplib/faddsub6m.s                     0x00000000   Number         0  faddsub.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/tanf.c                        0x00000000   Number         0  tanf.o ABSOLUTE
    ..\source\ti\devices\msp\m0p\startup_system_files\keil\startup_mspm0g350x_uvision.s 0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    FusionOffset.c                           0x00000000   Number         0  fusionoffset.o ABSOLUTE
    buzzer_app.c                             0x00000000   Number         0  buzzer_app.o ABSOLUTE
    calibration.c                            0x00000000   Number         0  calibration.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_aes.c                                 0x00000000   Number         0  dl_aes.o ABSOLUTE
    dl_aesadv.c                              0x00000000   Number         0  dl_aesadv.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_crc.c                                 0x00000000   Number         0  dl_crc.o ABSOLUTE
    dl_crcp.c                                0x00000000   Number         0  dl_crcp.o ABSOLUTE
    dl_dac12.c                               0x00000000   Number         0  dl_dac12.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_flashctl.c                            0x00000000   Number         0  dl_flashctl.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_interrupt.c                           0x00000000   Number         0  dl_interrupt.o ABSOLUTE
    dl_keystorectl.c                         0x00000000   Number         0  dl_keystorectl.o ABSOLUTE
    dl_lcd.c                                 0x00000000   Number         0  dl_lcd.o ABSOLUTE
    dl_lfss.c                                0x00000000   Number         0  dl_lfss.o ABSOLUTE
    dl_mathacl.c                             0x00000000   Number         0  dl_mathacl.o ABSOLUTE
    dl_mcan.c                                0x00000000   Number         0  dl_mcan.o ABSOLUTE
    dl_opa.c                                 0x00000000   Number         0  dl_opa.o ABSOLUTE
    dl_rtc_common.c                          0x00000000   Number         0  dl_rtc_common.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_trng.c                                0x00000000   Number         0  dl_trng.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    dl_vref.c                                0x00000000   Number         0  dl_vref.o ABSOLUTE
    encoder_driver.c                         0x00000000   Number         0  encoder_driver.o ABSOLUTE
    filter.c                                 0x00000000   Number         0  filter.o ABSOLUTE
    hui_app.c                                0x00000000   Number         0  hui_app.o ABSOLUTE
    icm20608.c                               0x00000000   Number         0  icm20608.o ABSOLUTE
    imu_app.c                                0x00000000   Number         0  imu_app.o ABSOLUTE
    interrupt_handler.c                      0x00000000   Number         0  interrupt_handler.o ABSOLUTE
    led_app.c                                0x00000000   Number         0  led_app.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    motor_app.c                              0x00000000   Number         0  motor_app.o ABSOLUTE
    nbutton.c                                0x00000000   Number         0  nbutton.o ABSOLUTE
    ni2c.c                                   0x00000000   Number         0  ni2c.o ABSOLUTE
    ntimer.c                                 0x00000000   Number         0  ntimer.o ABSOLUTE
    nuart.c                                  0x00000000   Number         0  nuart.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    pid.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    ssd1306.c                                0x00000000   Number         0  ssd1306.o ABSOLUTE
    system.c                                 0x00000000   Number         0  system.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    uart_app.c                               0x00000000   Number         0  uart_app.o ABSOLUTE
    uart_process.c                           0x00000000   Number         0  uart_process.o ABSOLUTE
    ui.c                                     0x00000000   Number         0  ui.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000120   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x00000140   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000148   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x00000164   Section        2  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$0000000A  0x00000166   Section       10  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$00000017  0x00000170   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x00000174   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000176   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000178   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0000017a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000017c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000017c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000017c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000182   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000182   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000186   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000186   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000018e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x00000190   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x00000190   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000194   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x0000019c   Section       56  rt_memcpy.o(.emb_text)
    .text                                    0x000001d4   Section       48  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x00000204   Section        0  _printf_dec.o(.text)
    .text                                    0x00000270   Section        0  rt_memcpy.o(.text)
    .text                                    0x000002f2   Section        0  rt_memclr.o(.text)
    .text                                    0x00000334   Section      504  aeabi_sdivfast.o(.text)
    .text                                    0x0000052c   Section        0  heapauxi.o(.text)
    .text                                    0x00000534   Section        0  fdiv.o(.text)
    .text                                    0x00000694   Section        0  ffixi.o(.text)
    .text                                    0x000006e0   Section        0  ffixui.o(.text)
    .text                                    0x00000710   Section        0  fflti.o(.text)
    .text                                    0x0000076e   Section        0  _rserrno.o(.text)
    .text                                    0x00000784   Section        0  _printf_intcommon.o(.text)
    .text                                    0x00000834   Section        0  rtudiv10.o(.text)
    .text                                    0x0000085c   Section        0  frnd.o(.text)
    .text                                    0x000008dc   Section        0  fsqrt.o(.text)
    .text                                    0x0000096c   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x00000974   Section        0  fcmpin.o(.text)
    .text                                    0x000009d8   Section        0  fscalbn.o(.text)
    .text                                    0x00000a1c   Section        8  libspace.o(.text)
    .text                                    0x00000a24   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x00000a62   Section        0  exit.o(.text)
    .text                                    0x00000a72   Section        0  cmpret.o(.text)
    .text                                    0x00000aa0   Section        0  fnan2.o(.text)
    .text                                    0x00000ab0   Section        0  retnan.o(.text)
    .text                                    0x00000b10   Section        0  sys_exit.o(.text)
    .text                                    0x00000b1c   Section        2  use_no_semi.o(.text)
    .text                                    0x00000b1e   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x00000b20   Section        0  nbutton.o(.text.Button_Init)
    __arm_cp.0_0                             0x00000b88   Number         4  nbutton.o(.text.Button_Init)
    __arm_cp.0_2                             0x00000b8c   Number         4  nbutton.o(.text.Button_Init)
    [Anonymous Symbol]                       0x00000b90   Section        0  buzzer_app.o(.text.Buzzer_Beep)
    [Anonymous Symbol]                       0x00000ba4   Section        0  buzzer_app.o(.text.Buzzer_Init)
    __arm_cp.0_0                             0x00000bcc   Number         4  buzzer_app.o(.text.Buzzer_Init)
    __arm_cp.0_1                             0x00000bd0   Number         4  buzzer_app.o(.text.Buzzer_Init)
    __arm_cp.0_2                             0x00000bd4   Number         4  buzzer_app.o(.text.Buzzer_Init)
    [Anonymous Symbol]                       0x00000bd8   Section        0  buzzer_app.o(.text.Buzzer_Work)
    __arm_cp.1_0                             0x00000c60   Number         4  buzzer_app.o(.text.Buzzer_Work)
    [Anonymous Symbol]                       0x00000c64   Section        0  calibration.o(.text.Calibration_Get_CRC32)
    [Anonymous Symbol]                       0x00000d5c   Section        0  calibration.o(.text.Calibration_Get_Status)
    [Anonymous Symbol]                       0x00000d64   Section        0  calibration.o(.text.Calibration_Save)
    [Anonymous Symbol]                       0x00000e78   Section        0  calibration.o(.text.Calibration_Set_Gyro_Offset)
    [Anonymous Symbol]                       0x00000e88   Section        0  calibration.o(.text.Calibration_Update)
    __arm_cp.12_0                            0x00000fc4   Number         4  calibration.o(.text.Calibration_Update)
    __arm_cp.12_1                            0x00000fc8   Number         4  calibration.o(.text.Calibration_Update)
    __arm_cp.12_2                            0x00000fcc   Number         4  calibration.o(.text.Calibration_Update)
    __arm_cp.12_3                            0x00000fd0   Number         4  calibration.o(.text.Calibration_Update)
    __arm_cp.12_4                            0x00000fd4   Number         4  calibration.o(.text.Calibration_Update)
    __arm_cp.12_5                            0x00000fd8   Number         4  calibration.o(.text.Calibration_Update)
    __arm_cp.12_6                            0x00000fdc   Number         4  calibration.o(.text.Calibration_Update)
    [Anonymous Symbol]                       0x00000fe0   Section        0  calibration.o(.text.Calibration_Verify_Integrity)
    __arm_cp.14_0                            0x000010e0   Number         4  calibration.o(.text.Calibration_Verify_Integrity)
    __arm_cp.14_1                            0x000010e4   Number         4  calibration.o(.text.Calibration_Verify_Integrity)
    [Anonymous Symbol]                       0x000010e8   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x00001120   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x00001124   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    [Anonymous Symbol]                       0x00001128   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x00001134   Section        0  dl_dma.o(.text.DL_DMA_initChannel)
    __arm_cp.0_0                             0x00001174   Number         4  dl_dma.o(.text.DL_DMA_initChannel)
    [Anonymous Symbol]                       0x00001178   Section        0  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    [Anonymous Symbol]                       0x000011f8   Section        0  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    __arm_cp.3_0                             0x00001238   Number         4  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    [Anonymous Symbol]                       0x0000123c   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    [Anonymous Symbol]                       0x00001264   Section        0  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_0                             0x000012a0   Number         4  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_1                             0x000012a4   Number         4  dl_spi.o(.text.DL_SPI_init)
    [Anonymous Symbol]                       0x000012a8   Section        0  dl_spi.o(.text.DL_SPI_setClockConfig)
    [Anonymous Symbol]                       0x000012bc   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_0                             0x00001398   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x0000139c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x000013a0   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x000013a4   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    [Anonymous Symbol]                       0x000013a8   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x0000140c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    [Anonymous Symbol]                       0x00001410   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00001458   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x0000145c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    [Anonymous Symbol]                       0x00001460   Section        0  dl_timer.o(.text.DL_TimerA_initPWMMode)
    __arm_cp.39_0                            0x000014cc   Number         4  dl_timer.o(.text.DL_TimerA_initPWMMode)
    [Anonymous Symbol]                       0x000014d0   Section        0  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_1                            0x0000157c   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_2                            0x00001580   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_3                            0x00001584   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_4                            0x00001588   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_6                            0x0000158c   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    [Anonymous Symbol]                       0x00001590   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x0000166c   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_1                             0x00001670   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x00001674   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    [Anonymous Symbol]                       0x00001678   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.26_0                            0x00001690   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00001694   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.13_0                            0x000016a8   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x000016ac   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x000016b8   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x000016bc   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x000016d4   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x000016d8   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00001718   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x0000171c   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x00001720   Section        0  dl_uart.o(.text.DL_UART_receiveDataCheck)
    [Anonymous Symbol]                       0x00001738   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x0000174c   Section        0  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    __arm_cp.7_0                             0x0000176c   Number         4  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    [Anonymous Symbol]                       0x00001770   Section        0  system.o(.text.Delay_Ms)
    __arm_cp.8_0                             0x00001820   Number         4  system.o(.text.Delay_Ms)
    __arm_cp.8_1                             0x00001824   Number         4  system.o(.text.Delay_Ms)
    __arm_cp.8_2                             0x00001828   Number         4  system.o(.text.Delay_Ms)
    [Anonymous Symbol]                       0x0000182c   Section        0  oled.o(.text.Draw_Logo)
    __arm_cp.18_0                            0x000018ec   Number         4  oled.o(.text.Draw_Logo)
    __arm_cp.18_1                            0x000018f0   Number         4  oled.o(.text.Draw_Logo)
    __arm_cp.18_2                            0x000018f4   Number         4  oled.o(.text.Draw_Logo)
    [Anonymous Symbol]                       0x000018f8   Section        0  encoder_driver.o(.text.Encoder_Init)
    __arm_cp.0_0                             0x00001904   Number         4  encoder_driver.o(.text.Encoder_Init)
    __arm_cp.0_1                             0x00001908   Number         4  encoder_driver.o(.text.Encoder_Init)
    [Anonymous Symbol]                       0x0000190c   Section        0  fusionoffset.o(.text.FusionOffsetUpdate)
    __arm_cp.1_0                             0x000019cc   Number         4  fusionoffset.o(.text.FusionOffsetUpdate)
    __arm_cp.1_1                             0x000019d0   Number         4  fusionoffset.o(.text.FusionOffsetUpdate)
    [Anonymous Symbol]                       0x000019d4   Section        0  interrupt_handler.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_0                             0x00001a18   Number         4  interrupt_handler.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_1                             0x00001a1c   Number         4  interrupt_handler.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x00001a20   Section        0  hui_app.o(.text.HUI_Get_Pin_State)
    [Anonymous Symbol]                       0x00001a30   Section        0  hui_app.o(.text.HUI_Read_All_Pins)
    __arm_cp.1_0                             0x00001a98   Number         4  hui_app.o(.text.HUI_Read_All_Pins)
    __arm_cp.1_1                             0x00001a9c   Number         4  hui_app.o(.text.HUI_Read_All_Pins)
    __arm_cp.1_2                             0x00001aa0   Number         4  hui_app.o(.text.HUI_Read_All_Pins)
    [Anonymous Symbol]                       0x00001aa4   Section        0  system.o(.text.HardFault_Handler)
    __arm_cp.14_0                            0x00001ab0   Number         4  system.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x00001ab4   Section        0  ni2c.o(.text.I2C_ReadReg)
    __arm_cp.1_0                             0x00001d4c   Number         4  ni2c.o(.text.I2C_ReadReg)
    __arm_cp.1_1                             0x00001d50   Number         4  ni2c.o(.text.I2C_ReadReg)
    __arm_cp.1_2                             0x00001d54   Number         4  ni2c.o(.text.I2C_ReadReg)
    __arm_cp.1_3                             0x00001d58   Number         4  ni2c.o(.text.I2C_ReadReg)
    __arm_cp.1_4                             0x00001d5c   Number         4  ni2c.o(.text.I2C_ReadReg)
    [Anonymous Symbol]                       0x00001d60   Section        0  icm20608.o(.text.ICM20608_Calibrate_Gyro)
    __arm_cp.1_3                             0x00001ea4   Number         4  icm20608.o(.text.ICM20608_Calibrate_Gyro)
    __arm_cp.1_4                             0x00001ea8   Number         4  icm20608.o(.text.ICM20608_Calibrate_Gyro)
    [Anonymous Symbol]                       0x00001eac   Section        0  icm20608.o(.text.ICM20608_Get_Angles)
    __arm_cp.17_0                            0x00001ebc   Number         4  icm20608.o(.text.ICM20608_Get_Angles)
    [Anonymous Symbol]                       0x00001ec0   Section        0  icm20608.o(.text.ICM20608_Get_Calib_Status)
    __arm_cp.2_0                             0x00001ed8   Number         4  icm20608.o(.text.ICM20608_Get_Calib_Status)
    __arm_cp.2_1                             0x00001edc   Number         4  icm20608.o(.text.ICM20608_Get_Calib_Status)
    [Anonymous Symbol]                       0x00001ee0   Section        0  icm20608.o(.text.ICM20608_Get_Calibration_Status)
    [Anonymous Symbol]                       0x00001ee8   Section        0  icm20608.o(.text.ICM20608_Get_Fusion_Status)
    [Anonymous Symbol]                       0x00001ef0   Section        0  icm20608.o(.text.ICM20608_Get_Fusion_Timer)
    [Anonymous Symbol]                       0x00001f00   Section        0  icm20608.o(.text.ICM20608_Get_Gyro_Offset)
    __arm_cp.3_0                             0x00001f28   Number         4  icm20608.o(.text.ICM20608_Get_Gyro_Offset)
    __arm_cp.3_1                             0x00001f2c   Number         4  icm20608.o(.text.ICM20608_Get_Gyro_Offset)
    __arm_cp.3_2                             0x00001f30   Number         4  icm20608.o(.text.ICM20608_Get_Gyro_Offset)
    [Anonymous Symbol]                       0x00001f34   Section        0  icm20608.o(.text.ICM20608_Get_Temperature_Range)
    __arm_cp.7_0                             0x00002098   Number         4  icm20608.o(.text.ICM20608_Get_Temperature_Range)
    __arm_cp.7_1                             0x0000209c   Number         4  icm20608.o(.text.ICM20608_Get_Temperature_Range)
    __arm_cp.7_2                             0x000020a0   Number         4  icm20608.o(.text.ICM20608_Get_Temperature_Range)
    [Anonymous Symbol]                       0x000020a4   Section        0  icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate)
    __arm_cp.9_0                             0x000020ac   Number         4  icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate)
    [Anonymous Symbol]                       0x000020b0   Section        0  icm20608.o(.text.ICM20608_Get_Yaw_Quality)
    __arm_cp.8_0                             0x000020b8   Number         4  icm20608.o(.text.ICM20608_Get_Yaw_Quality)
    [Anonymous Symbol]                       0x000020bc   Section        0  icm20608.o(.text.ICM20608_Is_Temperature_Stable)
    [Anonymous Symbol]                       0x000020c4   Section        0  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_0                            0x00002434   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_1                            0x00002438   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_2                            0x0000243c   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_3                            0x00002440   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_4                            0x00002444   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_5                            0x00002448   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_6                            0x0000244c   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_7                            0x00002450   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_8                            0x00002454   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_9                            0x00002458   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_10                           0x0000245c   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_11                           0x00002460   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_12                           0x00002464   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_13                           0x00002468   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_14                           0x0000246c   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_15                           0x00002470   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_16                           0x00002474   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    __arm_cp.15_17                           0x00002478   Number         4  icm20608.o(.text.ICM20608_Read_Data)
    [Anonymous Symbol]                       0x0000247c   Section        0  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_0                            0x00002808   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_1                            0x0000280c   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_2                            0x00002810   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_3                            0x00002814   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_4                            0x00002818   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_5                            0x0000281c   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_6                            0x00002820   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_7                            0x00002824   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_8                            0x00002828   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_9                            0x0000282c   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_10                           0x00002830   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_11                           0x00002834   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_12                           0x00002838   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_13                           0x0000283c   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_14                           0x00002840   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_15                           0x00002844   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_16                           0x00002848   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_17                           0x0000284c   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_18                           0x00002850   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_19                           0x00002854   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_20                           0x00002858   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_21                           0x0000285c   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_22                           0x00002860   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_23                           0x00002864   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_24                           0x00002868   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_25                           0x0000286c   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    __arm_cp.16_26                           0x00002870   Number         4  icm20608.o(.text.ICM20608_Update_Angles)
    [Anonymous Symbol]                       0x00002874   Section        0  imu_app.o(.text.IMU_App_Get_Angles)
    [Anonymous Symbol]                       0x00002884   Section        0  imu_app.o(.text.IMU_App_Get_Calib_Status)
    [Anonymous Symbol]                       0x0000288c   Section        0  imu_app.o(.text.IMU_App_Get_Calibration_Status)
    [Anonymous Symbol]                       0x00002894   Section        0  imu_app.o(.text.IMU_App_Get_Calibration_Status_String)
    [Anonymous Symbol]                       0x000028cc   Section        0  imu_app.o(.text.IMU_App_Get_Fusion_Status)
    [Anonymous Symbol]                       0x000028d4   Section        0  imu_app.o(.text.IMU_App_Get_Fusion_Timer)
    [Anonymous Symbol]                       0x000028dc   Section        0  imu_app.o(.text.IMU_App_Get_Gyro_Offset)
    [Anonymous Symbol]                       0x000028e4   Section        0  imu_app.o(.text.IMU_App_Get_Performance_Stats)
    [Anonymous Symbol]                       0x00002920   Section        0  imu_app.o(.text.IMU_App_Get_System_Status)
    [Anonymous Symbol]                       0x00002940   Section        0  imu_app.o(.text.IMU_App_Get_Temperature)
    [Anonymous Symbol]                       0x00002948   Section        0  imu_app.o(.text.IMU_App_Get_Temperature_Range)
    [Anonymous Symbol]                       0x00002950   Section        0  imu_app.o(.text.IMU_App_Get_Test_Results)
    [Anonymous Symbol]                       0x000029a0   Section        0  imu_app.o(.text.IMU_App_Get_Test_Status_String)
    __arm_cp.24_0                            0x000029f4   Number         4  imu_app.o(.text.IMU_App_Get_Test_Status_String)
    __arm_cp.24_1                            0x000029f8   Number         4  imu_app.o(.text.IMU_App_Get_Test_Status_String)
    __arm_cp.24_2                            0x000029fc   Number         4  imu_app.o(.text.IMU_App_Get_Test_Status_String)
    [Anonymous Symbol]                       0x00002a0c   Section        0  imu_app.o(.text.IMU_App_Get_Yaw_Drift_Rate)
    [Anonymous Symbol]                       0x00002a14   Section        0  imu_app.o(.text.IMU_App_Get_Yaw_Quality)
    [Anonymous Symbol]                       0x00002a1c   Section        0  imu_app.o(.text.IMU_App_Get_Yaw_Status_String)
    [Anonymous Symbol]                       0x00002a70   Section        0  imu_app.o(.text.IMU_App_Is_Ready)
    [Anonymous Symbol]                       0x00002a84   Section        0  imu_app.o(.text.IMU_App_Is_Stationary)
    [Anonymous Symbol]                       0x00002a96   Section        0  imu_app.o(.text.IMU_App_Is_Temperature_Stable)
    [Anonymous Symbol]                       0x00002aa0   Section        0  imu_app.o(.text.IMU_App_Update)
    __arm_cp.1_0                             0x00002b00   Number         4  imu_app.o(.text.IMU_App_Update)
    __arm_cp.1_1                             0x00002b04   Number         4  imu_app.o(.text.IMU_App_Update)
    __arm_cp.1_2                             0x00002b08   Number         4  imu_app.o(.text.IMU_App_Update)
    [Anonymous Symbol]                       0x00002b0c   Section        0  oled.o(.text.LCD_CLS)
    [Anonymous Symbol]                       0x00002c78   Section        0  oled.o(.text.LCD_P6x8Char)
    [Anonymous Symbol]                       0x00002cd8   Section        0  oled.o(.text.LCD_P6x8Str)
    __arm_cp.9_0                             0x00002d60   Number         4  oled.o(.text.LCD_P6x8Str)
    [Anonymous Symbol]                       0x00002d64   Section        0  oled.o(.text.LCD_clear_L)
    __arm_cp.17_1                            0x00002e4c   Number         4  oled.o(.text.LCD_clear_L)
    [Anonymous Symbol]                       0x00002e50   Section        0  led_app.o(.text.LED_App_Init)
    __arm_cp.0_0                             0x00002e64   Number         4  led_app.o(.text.LED_App_Init)
    [Anonymous Symbol]                       0x00002e68   Section        0  filter.o(.text.LPButterworth)
    __arm_cp.0_0                             0x00002fac   Number         4  filter.o(.text.LPButterworth)
    [Anonymous Symbol]                       0x00002fb0   Section        0  motor_app.o(.text.Motor_App_Init)
    __arm_cp.0_0                             0x00002fe0   Number         4  motor_app.o(.text.Motor_App_Init)
    [Anonymous Symbol]                       0x00002fe4   Section        0  system.o(.text.NMI_Handler)
    __arm_cp.13_0                            0x00002ff0   Number         4  system.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x00002ff4   Section        0  oled.o(.text.OLED_Init)
    __arm_cp.19_0                            0x0000301c   Number         4  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x00003020   Section        0  oled.o(.text.OLED_WrCmd)
    [Anonymous Symbol]                       0x000030a4   Section        0  oled.o(.text.OLED_WrDat)
    __arm_cp.0_0                             0x00003128   Number         4  oled.o(.text.OLED_WrDat)
    __arm_cp.0_1                             0x0000312c   Number         4  oled.o(.text.OLED_WrDat)
    [Anonymous Symbol]                       0x00003130   Section        0  encoder_driver.o(.text.QEI0_IRQHandler)
    [Anonymous Symbol]                       0x0000316c   Section        0  encoder_driver.o(.text.QEI1_IRQHandler)
    __arm_cp.2_0                             0x000031a8   Number         4  encoder_driver.o(.text.QEI1_IRQHandler)
    __arm_cp.2_1                             0x000031ac   Number         4  encoder_driver.o(.text.QEI1_IRQHandler)
    __arm_cp.2_2                             0x000031b0   Number         4  encoder_driver.o(.text.QEI1_IRQHandler)
    [Anonymous Symbol]                       0x000031b4   Section        0  nbutton.o(.text.Read_Button_State_One)
    __arm_cp.1_0                             0x00003280   Number         4  nbutton.o(.text.Read_Button_State_One)
    __arm_cp.1_1                             0x00003284   Number         4  nbutton.o(.text.Read_Button_State_One)
    [Anonymous Symbol]                       0x00003288   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.17_0                            0x000032d4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.17_1                            0x000032d8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.17_2                            0x000032dc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.17_3                            0x000032e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.17_4                            0x000032e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.17_5                            0x000032e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    [Anonymous Symbol]                       0x000032ec   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.23_0                            0x000032fc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.23_1                            0x00003300   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    [Anonymous Symbol]                       0x00003304   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    [Anonymous Symbol]                       0x0000330c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00003428   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x0000342c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00003430   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00003434   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00003438   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x0000343c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00003440   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x00003444   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_8                             0x00003448   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_9                             0x0000344c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_10                            0x00003450   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_11                            0x00003454   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00003458   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.11_0                            0x000034a0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.11_1                            0x000034a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.11_2                            0x000034a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    [Anonymous Symbol]                       0x000034ac   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_0                             0x00003564   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_1                             0x00003568   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_2                             0x0000356c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_3                             0x00003570   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    [Anonymous Symbol]                       0x00003574   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init)
    __arm_cp.5_0                             0x000035e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init)
    __arm_cp.5_1                             0x000035e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init)
    __arm_cp.5_2                             0x000035e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init)
    __arm_cp.5_3                             0x000035ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init)
    [Anonymous Symbol]                       0x000035f0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init)
    __arm_cp.6_0                             0x0000365c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init)
    __arm_cp.6_1                             0x00003660   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init)
    __arm_cp.6_2                             0x00003664   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init)
    __arm_cp.6_3                             0x00003668   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init)
    [Anonymous Symbol]                       0x0000366c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init)
    __arm_cp.16_0                            0x000036a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init)
    __arm_cp.16_1                            0x000036a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init)
    __arm_cp.16_2                            0x000036ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init)
    __arm_cp.16_3                            0x000036b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init)
    __arm_cp.16_4                            0x000036b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init)
    [Anonymous Symbol]                       0x000036b8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    __arm_cp.20_0                            0x000036e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    __arm_cp.20_1                            0x000036e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    [Anonymous Symbol]                       0x000036e8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00003778   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x0000377c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_2                             0x00003780   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00003784   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.19_0                            0x000037a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.19_1                            0x000037ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.19_2                            0x000037b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x000037b4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init)
    __arm_cp.7_0                             0x000037e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init)
    __arm_cp.7_1                             0x000037ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init)
    __arm_cp.7_2                             0x000037f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init)
    __arm_cp.7_3                             0x000037f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init)
    __arm_cp.7_5                             0x000037f8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init)
    [Anonymous Symbol]                       0x000037fc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init)
    __arm_cp.10_0                            0x00003830   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init)
    __arm_cp.10_1                            0x00003834   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init)
    __arm_cp.10_2                            0x00003838   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init)
    __arm_cp.10_3                            0x0000383c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init)
    __arm_cp.10_4                            0x00003840   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init)
    __arm_cp.10_5                            0x00003844   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init)
    [Anonymous Symbol]                       0x00003848   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init)
    __arm_cp.8_0                             0x00003880   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init)
    __arm_cp.8_1                             0x00003884   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init)
    __arm_cp.8_2                             0x00003888   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init)
    __arm_cp.8_3                             0x0000388c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init)
    __arm_cp.8_4                             0x00003890   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init)
    __arm_cp.8_5                             0x00003894   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init)
    [Anonymous Symbol]                       0x00003898   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init)
    __arm_cp.9_0                             0x000038d0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init)
    __arm_cp.9_1                             0x000038d4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init)
    __arm_cp.9_2                             0x000038d8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init)
    __arm_cp.9_3                             0x000038dc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init)
    __arm_cp.9_5                             0x000038e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init)
    [Anonymous Symbol]                       0x000038e4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.12_0                            0x0000394c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.12_1                            0x00003950   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.12_2                            0x00003954   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.12_3                            0x00003958   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.12_5                            0x0000395c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00003960   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.13_0                            0x000039c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.13_1                            0x000039c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.13_2                            0x000039cc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.13_3                            0x000039d0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.13_5                            0x000039d4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    [Anonymous Symbol]                       0x000039d8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.14_0                            0x00003a60   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.14_1                            0x00003a64   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.14_2                            0x00003a68   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.14_3                            0x00003a6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.14_5                            0x00003a70   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.14_6                            0x00003a74   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    [Anonymous Symbol]                       0x00003a78   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.15_0                            0x00003ae4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.15_1                            0x00003ae8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.15_2                            0x00003aec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.15_3                            0x00003af0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.15_4                            0x00003af4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.15_5                            0x00003af8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.15_6                            0x00003afc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    [Anonymous Symbol]                       0x00003b00   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00003b78   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00003b7c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x00003b80   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_3                             0x00003b84   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_4                             0x00003b88   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_5                             0x00003b8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00003b90   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00003c18   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00003c1c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00003c20   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00003c24   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00003c28   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00003c2c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00003c30   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x00003c34   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x00003c38   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_9                             0x00003c3c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_10                            0x00003c40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_11                            0x00003c44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_12                            0x00003c48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_13                            0x00003c4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_14                            0x00003c50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_15                            0x00003c54   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_16                            0x00003c58   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_17                            0x00003c5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00003c60   Section        0  system.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x00003c6c   Section        0  ntimer.o(.text.TIMG0_IRQHandler)
    __arm_cp.2_0                             0x00003c80   Number         4  ntimer.o(.text.TIMG0_IRQHandler)
    __arm_cp.2_1                             0x00003c84   Number         4  ntimer.o(.text.TIMG0_IRQHandler)
    [Anonymous Symbol]                       0x00003c88   Section        0  ntimer.o(.text.TIMG12_IRQHandler)
    __arm_cp.5_0                             0x00003ca0   Number         4  ntimer.o(.text.TIMG12_IRQHandler)
    __arm_cp.5_1                             0x00003ca4   Number         4  ntimer.o(.text.TIMG12_IRQHandler)
    [Anonymous Symbol]                       0x00003ca8   Section        0  ntimer.o(.text.TIMG6_IRQHandler)
    __arm_cp.3_0                             0x00003cc4   Number         4  ntimer.o(.text.TIMG6_IRQHandler)
    __arm_cp.3_1                             0x00003cc8   Number         4  ntimer.o(.text.TIMG6_IRQHandler)
    __arm_cp.3_2                             0x00003ccc   Number         4  ntimer.o(.text.TIMG6_IRQHandler)
    [Anonymous Symbol]                       0x00003cd0   Section        0  ntimer.o(.text.TIMG8_IRQHandler)
    __arm_cp.4_0                             0x00003ce4   Number         4  ntimer.o(.text.TIMG8_IRQHandler)
    __arm_cp.4_1                             0x00003ce8   Number         4  ntimer.o(.text.TIMG8_IRQHandler)
    [Anonymous Symbol]                       0x00003cec   Section        0  uart_app.o(.text.UART0_App_GetRxData)
    [Anonymous Symbol]                       0x00003d28   Section        0  uart_app.o(.text.UART0_App_Init)
    __arm_cp.0_0                             0x00003d44   Number         4  uart_app.o(.text.UART0_App_Init)
    __arm_cp.0_1                             0x00003d48   Number         4  uart_app.o(.text.UART0_App_Init)
    [Anonymous Symbol]                       0x00003d4c   Section        0  uart_app.o(.text.UART0_App_IsRxComplete)
    [Anonymous Symbol]                       0x00003d54   Section        0  uart_app.o(.text.UART0_App_RxCallback)
    __arm_cp.9_1                             0x00003d8c   Number         4  uart_app.o(.text.UART0_App_RxCallback)
    [Anonymous Symbol]                       0x00003d90   Section        0  uart_app.o(.text.UART0_App_RxProcess)
    __arm_cp.5_0                             0x00003db0   Number         4  uart_app.o(.text.UART0_App_RxProcess)
    __arm_cp.5_1                             0x00003db4   Number         4  uart_app.o(.text.UART0_App_RxProcess)
    __arm_cp.5_2                             0x00003db8   Number         4  uart_app.o(.text.UART0_App_RxProcess)
    [Anonymous Symbol]                       0x00003dbc   Section        0  uart_app.o(.text.UART0_App_SendBytes)
    [Anonymous Symbol]                       0x00003dd8   Section        0  nuart.o(.text.UART0_IRQHandler)
    __arm_cp.1_0                             0x00003e0c   Number         4  nuart.o(.text.UART0_IRQHandler)
    __arm_cp.1_1                             0x00003e10   Number         4  nuart.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x00003e14   Section        0  nuart.o(.text.UART1_IRQHandler)
    __arm_cp.2_0                             0x00003e28   Number         4  nuart.o(.text.UART1_IRQHandler)
    __arm_cp.2_1                             0x00003e2c   Number         4  nuart.o(.text.UART1_IRQHandler)
    [Anonymous Symbol]                       0x00003e30   Section        0  nuart.o(.text.UART2_IRQHandler)
    __arm_cp.3_0                             0x00003e54   Number         4  nuart.o(.text.UART2_IRQHandler)
    __arm_cp.3_1                             0x00003e58   Number         4  nuart.o(.text.UART2_IRQHandler)
    __arm_cp.3_2                             0x00003e5c   Number         4  nuart.o(.text.UART2_IRQHandler)
    [Anonymous Symbol]                       0x00003e60   Section        0  nuart.o(.text.UART3_IRQHandler)
    __arm_cp.4_0                             0x00003e74   Number         4  nuart.o(.text.UART3_IRQHandler)
    __arm_cp.4_1                             0x00003e78   Number         4  nuart.o(.text.UART3_IRQHandler)
    [Anonymous Symbol]                       0x00003e7c   Section        0  system.o(.text.delay_ms)
    __arm_cp.6_0                             0x00003f2c   Number         4  system.o(.text.delay_ms)
    [Anonymous Symbol]                       0x00003f30   Section        0  oled.o(.text.display_6_8_number)
    [Anonymous Symbol]                       0x00003f38   Section        0  oled.o(.text.display_6_8_string)
    [Anonymous Symbol]                       0x00003f40   Section        0  nbutton.o(.text.get_key_short_press)
    [Anonymous Symbol]                       0x00003f90   Section        0  encoder_driver.o(.text.get_left_motor_speed)
    __arm_cp.3_0                             0x00004030   Number         4  encoder_driver.o(.text.get_left_motor_speed)
    __arm_cp.3_3                             0x00004034   Number         4  encoder_driver.o(.text.get_left_motor_speed)
    __arm_cp.3_5                             0x00004038   Number         4  encoder_driver.o(.text.get_left_motor_speed)
    __arm_cp.3_6                             0x0000403c   Number         4  encoder_driver.o(.text.get_left_motor_speed)
    __arm_cp.3_8                             0x00004040   Number         4  encoder_driver.o(.text.get_left_motor_speed)
    __arm_cp.3_12                            0x00004044   Number         4  encoder_driver.o(.text.get_left_motor_speed)
    [Anonymous Symbol]                       0x00004048   Section        0  encoder_driver.o(.text.get_right_motor_speed)
    __arm_cp.4_0                             0x000040c4   Number         4  encoder_driver.o(.text.get_right_motor_speed)
    __arm_cp.4_1                             0x000040c8   Number         4  encoder_driver.o(.text.get_right_motor_speed)
    __arm_cp.4_2                             0x000040cc   Number         4  encoder_driver.o(.text.get_right_motor_speed)
    __arm_cp.4_3                             0x000040d0   Number         4  encoder_driver.o(.text.get_right_motor_speed)
    __arm_cp.4_4                             0x000040d4   Number         4  encoder_driver.o(.text.get_right_motor_speed)
    __arm_cp.4_5                             0x000040d8   Number         4  encoder_driver.o(.text.get_right_motor_speed)
    __arm_cp.4_6                             0x000040dc   Number         4  encoder_driver.o(.text.get_right_motor_speed)
    __arm_cp.4_7                             0x000040e0   Number         4  encoder_driver.o(.text.get_right_motor_speed)
    __arm_cp.4_8                             0x000040e4   Number         4  encoder_driver.o(.text.get_right_motor_speed)
    [Anonymous Symbol]                       0x000040e8   Section        0  system.o(.text.get_systime)
    __arm_cp.10_3                            0x00004134   Number         4  system.o(.text.get_systime)
    [Anonymous Symbol]                       0x00004138   Section        0  encoder_driver.o(.text.get_wheel_speed)
    [Anonymous Symbol]                       0x00004144   Section        0  ni2c.o(.text.i2creadnbyte)
    [Anonymous Symbol]                       0x0000414c   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x00004188   Section        0  system.o(.text.millis)
    __arm_cp.3_0                             0x000041ac   Number         4  system.o(.text.millis)
    __arm_cp.3_1                             0x000041b0   Number         4  system.o(.text.millis)
    __arm_cp.3_2                             0x000041b4   Number         4  system.o(.text.millis)
    [Anonymous Symbol]                       0x000041b8   Section        0  system.o(.text.ncontroller_set_priority)
    __arm_cp.1_0                             0x00004224   Number         4  system.o(.text.ncontroller_set_priority)
    __arm_cp.1_1                             0x00004228   Number         4  system.o(.text.ncontroller_set_priority)
    __arm_cp.1_2                             0x0000422c   Number         4  system.o(.text.ncontroller_set_priority)
    [Anonymous Symbol]                       0x00004230   Section        0  nbutton.o(.text.read_button_state_all)
    __arm_cp.2_0                             0x00004280   Number         4  nbutton.o(.text.read_button_state_all)
    [Anonymous Symbol]                       0x00004284   Section        0  ui.o(.text.screen_display)
    __arm_cp.1_139                           0x000042f0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_140                           0x000042f4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_141                           0x00004608   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_142                           0x0000460c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_143                           0x00004610   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_144                           0x00004614   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_145                           0x00004618   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_146                           0x0000461c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_147                           0x00004620   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_148                           0x00004624   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_149                           0x00004628   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_150                           0x0000462c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_151                           0x00004630   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_152                           0x00004634   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_153                           0x00004638   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_154                           0x0000463c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_155                           0x00004640   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_156                           0x00004644   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_157                           0x00004648   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_158                           0x0000464c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_159                           0x00004650   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_160                           0x00004654   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_161                           0x00004658   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_162                           0x0000465c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_169                           0x00004678   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_170                           0x0000467c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_172                           0x00004684   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_173                           0x00004688   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_174                           0x0000468c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_175                           0x00004a6c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_176                           0x00004a70   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_177                           0x00004a74   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_178                           0x00004a78   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_180                           0x00004a80   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_181                           0x00004a84   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_182                           0x00004a88   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_183                           0x00004a8c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_184                           0x00004a90   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_185                           0x00004a94   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_186                           0x00004a98   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_187                           0x00004a9c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_188                           0x00004aa0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_189                           0x00004aa4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_190                           0x00004aa8   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_191                           0x00004aac   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_192                           0x00004ab0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_193                           0x00004ab4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_194                           0x00004ab8   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_195                           0x00004abc   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_196                           0x00004ac0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_197                           0x00004ac4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_198                           0x00004ac8   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_199                           0x00004acc   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_200                           0x00004ad0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_201                           0x00004ad4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_202                           0x00004ad8   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_203                           0x00004cac   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_204                           0x00004cb0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_205                           0x00004cec   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_206                           0x00004d0c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_208                           0x00004d34   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_209                           0x00004d38   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_210                           0x00004d3c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_213                           0x00004d54   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_215                           0x00004d60   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_218                           0x00004d78   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_235                           0x00004fd4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_236                           0x00004fd8   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_237                           0x00004fdc   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_238                           0x00004fe0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_239                           0x00004fe4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_240                           0x00004fe8   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_241                           0x00004fec   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_24                            0x000050d0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_60                            0x000050d4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_62                            0x000050d8   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_63                            0x000050dc   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_64                            0x000050e0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_65                            0x000050e4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_66                            0x000050e8   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_71                            0x000050f0   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_72                            0x000050f4   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_73                            0x000050f8   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_74                            0x000050fc   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_87                            0x00005100   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_89                            0x00005104   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_90                            0x00005108   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_91                            0x0000510c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_93                            0x00005114   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_96                            0x00005120   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_101                           0x00005134   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_103                           0x0000513c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_104                           0x00005140   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_105                           0x00005144   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_106                           0x00005148   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_107                           0x0000514c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_108                           0x00005150   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_118                           0x00005158   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_122                           0x00005168   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_123                           0x0000516c   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_124                           0x00005170   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_125                           0x00005174   Number         4  ui.o(.text.screen_display)
    __arm_cp.1_128                           0x00005178   Number         4  ui.o(.text.screen_display)
    [Anonymous Symbol]                       0x0000517c   Section        0  filter.o(.text.set_cutoff_frequency)
    __arm_cp.1_0                             0x00005224   Number         4  filter.o(.text.set_cutoff_frequency)
    __arm_cp.1_1                             0x00005228   Number         4  filter.o(.text.set_cutoff_frequency)
    __arm_cp.1_2                             0x0000522c   Number         4  filter.o(.text.set_cutoff_frequency)
    __arm_cp.1_3                             0x00005230   Number         4  filter.o(.text.set_cutoff_frequency)
    [Anonymous Symbol]                       0x00005234   Section        0  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_0                             0x0000533c   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_1                             0x00005340   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_2                             0x00005344   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_3                             0x00005348   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_4                             0x0000534c   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_5                             0x00005350   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_6                             0x00005354   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_7                             0x00005358   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_8                             0x0000535c   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_9                             0x00005360   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_10                            0x00005364   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_11                            0x00005368   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_12                            0x0000536c   Number         4  ssd1306.o(.text.ssd1306_begin)
    [Anonymous Symbol]                       0x00005370   Section        0  ntimer.o(.text.timer_irq_config)
    __arm_cp.0_0                             0x000053c0   Number         4  ntimer.o(.text.timer_irq_config)
    __arm_cp.0_1                             0x000053c4   Number         4  ntimer.o(.text.timer_irq_config)
    __arm_cp.0_2                             0x000053c8   Number         4  ntimer.o(.text.timer_irq_config)
    __arm_cp.0_4                             0x000053cc   Number         4  ntimer.o(.text.timer_irq_config)
    __arm_cp.0_5                             0x000053d0   Number         4  ntimer.o(.text.timer_irq_config)
    __arm_cp.0_6                             0x000053d4   Number         4  ntimer.o(.text.timer_irq_config)
    __arm_cp.0_7                             0x000053d8   Number         4  ntimer.o(.text.timer_irq_config)
    [Anonymous Symbol]                       0x000053dc   Section        0  uart_process.o(.text.uart_process)
    [Anonymous Symbol]                       0x00005408   Section        0  nuart.o(.text.usart_irq_config)
    __arm_cp.0_0                             0x00005448   Number         4  nuart.o(.text.usart_irq_config)
    __arm_cp.0_1                             0x0000544c   Number         4  nuart.o(.text.usart_irq_config)
    __arm_cp.0_2                             0x00005450   Number         4  nuart.o(.text.usart_irq_config)
    __arm_cp.0_3                             0x00005454   Number         4  nuart.o(.text.usart_irq_config)
    __arm_cp.0_4                             0x00005458   Number         4  nuart.o(.text.usart_irq_config)
    __arm_cp.0_5                             0x0000545c   Number         4  nuart.o(.text.usart_irq_config)
    __arm_cp.0_6                             0x00005460   Number         4  nuart.o(.text.usart_irq_config)
    [Anonymous Symbol]                       0x00005464   Section        0  oled.o(.text.write_6_8_number)
    __arm_cp.11_0                            0x00005708   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_1                            0x0000570c   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_2                            0x00005710   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_3                            0x00005714   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_4                            0x00005718   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_5                            0x0000571c   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_6                            0x00005720   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_7                            0x00005724   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_8                            0x00005728   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_9                            0x0000572c   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_10                           0x00005730   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_11                           0x00005734   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_12                           0x00005738   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_13                           0x0000573c   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_14                           0x00005740   Number         4  oled.o(.text.write_6_8_number)
    __arm_cp.11_15                           0x00005744   Number         4  oled.o(.text.write_6_8_number)
    .text_divfast                            0x00005748   Section      502  aeabi_sdivfast.o(.text_divfast)
    i.__ARM_common_ll_muluu                  0x0000593e   Section        0  rredf.o(i.__ARM_common_ll_muluu)
    i.__ARM_fpclassifyf                      0x00005970   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__mathlib_flt_infnan                   0x00005992   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x0000599c   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x000059a4   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x000059b0   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x000059c0   Section        0  rredf.o(i.__mathlib_rredf2)
    i._feq                                   0x00005b40   Section        0  fcmp.o(i._feq)
    i._fgeq                                  0x00005b56   Section        0  fcmp.o(i._fgeq)
    i._fgr                                   0x00005b6c   Section        0  fcmp.o(i._fgr)
    i._fleq                                  0x00005b82   Section        0  fcmp.o(i._fleq)
    i._fls                                   0x00005b9c   Section        0  fcmp.o(i._fls)
    i.atan2f                                 0x00005bb4   Section        0  atan2f.o(i.atan2f)
    i.sqrtf                                  0x00005e50   Section        0  sqrtf.o(i.sqrtf)
    i.tanf                                   0x00005e7c   Section        0  tanf.o(i.tanf)
    x$fpl$fadd                               0x00005fec   Section      140  faddsub.o(x$fpl$fadd)
    _fadd1                                   0x00005ff9   Thumb Code     0  faddsub.o(x$fpl$fadd)
    x$fpl$feqf                               0x00006078   Section       84  feqf.o(x$fpl$feqf)
    x$fpl$fgeqf                              0x000060cc   Section       84  fgef.o(x$fpl$fgeqf)
    x$fpl$fleqf                              0x00006120   Section       84  flef.o(x$fpl$fleqf)
    x$fpl$fmul                               0x00006174   Section      176  fmul.o(x$fpl$fmul)
    x$fpl$frsb                               0x00006224   Section       24  faddsub.o(x$fpl$frsb)
    x$fpl$fsub                               0x0000623c   Section      208  faddsub.o(x$fpl$fsub)
    _fsub1                                   0x00006249   Thumb Code     0  faddsub.o(x$fpl$fsub)
    fdiv_tab                                 0x0000630c   Data          64  fdiv.o(.constdata)
    .constdata                               0x0000630c   Section       64  fdiv.o(.constdata)
    x$fpl$usenofp                            0x0000630c   Section        0  usenofp.o(x$fpl$usenofp)
    twooverpi                                0x0000634c   Data          32  rredf.o(.constdata)
    .constdata                               0x0000634c   Section       32  rredf.o(.constdata)
    crc32_table                              0x00006994   Data          64  calibration.o(.rodata.crc32_table)
    [Anonymous Symbol]                       0x00006994   Section        0  calibration.o(.rodata.crc32_table)
    gADC12_0ClockConfig                      0x000069d4   Data           8  ti_msp_dl_config.o(.rodata.gADC12_0ClockConfig)
    [Anonymous Symbol]                       0x000069d4   Section        0  ti_msp_dl_config.o(.rodata.gADC12_0ClockConfig)
    gDMA_CH0Config                           0x000069dc   Data          24  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    [Anonymous Symbol]                       0x000069dc   Section        0  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    gI2C_0ClockConfig                        0x000069f4   Data           2  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    [Anonymous Symbol]                       0x000069f4   Section        0  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    gPWM_0ClockConfig                        0x000069f6   Data           3  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    [Anonymous Symbol]                       0x000069f6   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    gPWM_0Config                             0x000069fc   Data           8  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    [Anonymous Symbol]                       0x000069fc   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    gPWM_1ClockConfig                        0x00006a04   Data           3  ti_msp_dl_config.o(.rodata.gPWM_1ClockConfig)
    [Anonymous Symbol]                       0x00006a04   Section        0  ti_msp_dl_config.o(.rodata.gPWM_1ClockConfig)
    gPWM_1Config                             0x00006a08   Data           8  ti_msp_dl_config.o(.rodata.gPWM_1Config)
    [Anonymous Symbol]                       0x00006a08   Section        0  ti_msp_dl_config.o(.rodata.gPWM_1Config)
    gPWM_2ClockConfig                        0x00006a10   Data           3  ti_msp_dl_config.o(.rodata.gPWM_2ClockConfig)
    [Anonymous Symbol]                       0x00006a10   Section        0  ti_msp_dl_config.o(.rodata.gPWM_2ClockConfig)
    gPWM_2Config                             0x00006a14   Data           8  ti_msp_dl_config.o(.rodata.gPWM_2Config)
    [Anonymous Symbol]                       0x00006a14   Section        0  ti_msp_dl_config.o(.rodata.gPWM_2Config)
    gSPI_0_clockConfig                       0x00006a1c   Data           2  ti_msp_dl_config.o(.rodata.gSPI_0_clockConfig)
    [Anonymous Symbol]                       0x00006a1c   Section        0  ti_msp_dl_config.o(.rodata.gSPI_0_clockConfig)
    gSPI_0_config                            0x00006a1e   Data          10  ti_msp_dl_config.o(.rodata.gSPI_0_config)
    [Anonymous Symbol]                       0x00006a1e   Section        0  ti_msp_dl_config.o(.rodata.gSPI_0_config)
    gSYSPLLConfig                            0x00006a28   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x00006a28   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gTIMER_G0ClockConfig                     0x00006a50   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_G0ClockConfig)
    [Anonymous Symbol]                       0x00006a50   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_G0ClockConfig)
    gTIMER_G0TimerConfig                     0x00006a54   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_G0TimerConfig)
    [Anonymous Symbol]                       0x00006a54   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_G0TimerConfig)
    gTIMER_G12ClockConfig                    0x00006a68   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_G12ClockConfig)
    [Anonymous Symbol]                       0x00006a68   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_G12ClockConfig)
    gTIMER_G12TimerConfig                    0x00006a6c   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_G12TimerConfig)
    [Anonymous Symbol]                       0x00006a6c   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_G12TimerConfig)
    gTIMER_G6ClockConfig                     0x00006a80   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_G6ClockConfig)
    [Anonymous Symbol]                       0x00006a80   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_G6ClockConfig)
    gTIMER_G6TimerConfig                     0x00006a84   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_G6TimerConfig)
    [Anonymous Symbol]                       0x00006a84   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_G6TimerConfig)
    gTIMER_G8ClockConfig                     0x00006a98   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_G8ClockConfig)
    [Anonymous Symbol]                       0x00006a98   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_G8ClockConfig)
    gTIMER_G8TimerConfig                     0x00006a9c   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_G8TimerConfig)
    [Anonymous Symbol]                       0x00006a9c   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_G8TimerConfig)
    gUART_0ClockConfig                       0x00006ab0   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00006ab0   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x00006ab2   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00006ab2   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_1ClockConfig                       0x00006abc   Data           2  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    [Anonymous Symbol]                       0x00006abc   Section        0  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    gUART_1Config                            0x00006abe   Data          10  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x00006abe   Section        0  ti_msp_dl_config.o(.rodata.gUART_1Config)
    gUART_2ClockConfig                       0x00006ac8   Data           2  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    [Anonymous Symbol]                       0x00006ac8   Section        0  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    gUART_2Config                            0x00006aca   Data          10  ti_msp_dl_config.o(.rodata.gUART_2Config)
    [Anonymous Symbol]                       0x00006aca   Section        0  ti_msp_dl_config.o(.rodata.gUART_2Config)
    gUART_3ClockConfig                       0x00006ad4   Data           2  ti_msp_dl_config.o(.rodata.gUART_3ClockConfig)
    [Anonymous Symbol]                       0x00006ad4   Section        0  ti_msp_dl_config.o(.rodata.gUART_3ClockConfig)
    gUART_3Config                            0x00006ad6   Data          10  ti_msp_dl_config.o(.rodata.gUART_3Config)
    [Anonymous Symbol]                       0x00006ad6   Section        0  ti_msp_dl_config.o(.rodata.gUART_3Config)
    [Anonymous Symbol]                       0x00006ae0   Section        0  ui.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x00006eeb   Section        0  imu_app.o(.rodata.str1.1)
    auto_save_enabled                        0x2020003c   Data           1  calibration.o(.data.auto_save_enabled)
    [Anonymous Symbol]                       0x2020003c   Section        0  calibration.o(.data.auto_save_enabled)
    yaw_quality_score                        0x20200050   Data           4  icm20608.o(.data.yaw_quality_score)
    [Anonymous Symbol]                       0x20200050   Section        0  icm20608.o(.data.yaw_quality_score)
    .bss                                     0x20200058   Section       96  libspace.o(.bss)
    HEIGHT                                   0x202000b8   Data           1  ssd1306.o(.bss.HEIGHT)
    [Anonymous Symbol]                       0x202000b8   Section        0  ssd1306.o(.bss.HEIGHT)
    ICM20608_Update_Angles.prev_gyro_z       0x202000bc   Data           4  icm20608.o(.bss.ICM20608_Update_Angles.prev_gyro_z)
    [Anonymous Symbol]                       0x202000bc   Section        0  icm20608.o(.bss.ICM20608_Update_Angles.prev_gyro_z)
    IMU_App_Update.retry_count               0x202000c0   Data           2  imu_app.o(.bss.IMU_App_Update.retry_count)
    [Anonymous Symbol]                       0x202000c0   Section        0  imu_app.o(.bss.IMU_App_Update.retry_count)
    WIDTH                                    0x202000c2   Data           1  ssd1306.o(.bss.WIDTH)
    [Anonymous Symbol]                       0x202000c2   Section        0  ssd1306.o(.bss.WIDTH)
    _height                                  0x202001e6   Data           2  ssd1306.o(.bss._height)
    [Anonymous Symbol]                       0x202001e6   Section        0  ssd1306.o(.bss._height)
    _vccstate                                0x202001e8   Data           1  ssd1306.o(.bss._vccstate)
    [Anonymous Symbol]                       0x202001e8   Section        0  ssd1306.o(.bss._vccstate)
    _width                                   0x202001ea   Data           2  ssd1306.o(.bss._width)
    [Anonymous Symbol]                       0x202001ea   Section        0  ssd1306.o(.bss._width)
    accel_lpf_buf                            0x202001ec   Data          72  icm20608.o(.bss.accel_lpf_buf)
    [Anonymous Symbol]                       0x202001ec   Section        0  icm20608.o(.bss.accel_lpf_buf)
    accel_lpf_param                          0x20200234   Data          72  icm20608.o(.bss.accel_lpf_param)
    [Anonymous Symbol]                       0x20200234   Section        0  icm20608.o(.bss.accel_lpf_param)
    auto_calib_requested                     0x2020027c   Data           1  imu_app.o(.bss.auto_calib_requested)
    [Anonymous Symbol]                       0x2020027c   Section        0  imu_app.o(.bss.auto_calib_requested)
    calib_data                               0x2020029c   Data         100  calibration.o(.bss.calib_data)
    [Anonymous Symbol]                       0x2020029c   Section        0  calibration.o(.bss.calib_data)
    calib_status                             0x20200300   Data           1  calibration.o(.bss.calib_status)
    [Anonymous Symbol]                       0x20200300   Section        0  calibration.o(.bss.calib_status)
    cursor_x                                 0x20200302   Data           2  ssd1306.o(.bss.cursor_x)
    [Anonymous Symbol]                       0x20200302   Section        0  ssd1306.o(.bss.cursor_x)
    cursor_y                                 0x20200304   Data           2  ssd1306.o(.bss.cursor_y)
    [Anonymous Symbol]                       0x20200304   Section        0  ssd1306.o(.bss.cursor_y)
    data_changed                             0x20200306   Data           1  calibration.o(.bss.data_changed)
    [Anonymous Symbol]                       0x20200306   Section        0  calibration.o(.bss.data_changed)
    display_counter                          0x20200308   Data           4  ui.o(.bss.display_counter)
    [Anonymous Symbol]                       0x20200308   Section        0  ui.o(.bss.display_counter)
    filter_initialized                       0x2020030c   Data           1  encoder_driver.o(.bss.filter_initialized)
    [Anonymous Symbol]                       0x2020030c   Section        0  encoder_driver.o(.bss.filter_initialized)
    fusion_offset                            0x20200310   Data          24  icm20608.o(.bss.fusion_offset)
    [Anonymous Symbol]                       0x20200310   Section        0  icm20608.o(.bss.fusion_offset)
    fusion_offset_init                       0x20200328   Data           1  icm20608.o(.bss.fusion_offset_init)
    [Anonymous Symbol]                       0x20200328   Section        0  icm20608.o(.bss.fusion_offset_init)
    get_left_motor_speed.cnt1                0x202005ec   Data           2  encoder_driver.o(.bss.get_left_motor_speed.cnt1)
    [Anonymous Symbol]                       0x202005ec   Section        0  encoder_driver.o(.bss.get_left_motor_speed.cnt1)
    get_right_motor_speed.cnt2               0x202005ee   Data           2  encoder_driver.o(.bss.get_right_motor_speed.cnt2)
    [Anonymous Symbol]                       0x202005ee   Section        0  encoder_driver.o(.bss.get_right_motor_speed.cnt2)
    gyro_calib_done                          0x202005f0   Data           1  icm20608.o(.bss.gyro_calib_done)
    [Anonymous Symbol]                       0x202005f0   Section        0  icm20608.o(.bss.gyro_calib_done)
    gyro_calib_in_progress                   0x202005f1   Data           1  icm20608.o(.bss.gyro_calib_in_progress)
    [Anonymous Symbol]                       0x202005f1   Section        0  icm20608.o(.bss.gyro_calib_in_progress)
    gyro_lpf_buf                             0x202005f4   Data          72  icm20608.o(.bss.gyro_lpf_buf)
    [Anonymous Symbol]                       0x202005f4   Section        0  icm20608.o(.bss.gyro_lpf_buf)
    gyro_lpf_param                           0x2020063c   Data          72  icm20608.o(.bss.gyro_lpf_param)
    [Anonymous Symbol]                       0x2020063c   Section        0  icm20608.o(.bss.gyro_lpf_param)
    gyro_offset                              0x20200684   Data          12  icm20608.o(.bss.gyro_offset)
    [Anonymous Symbol]                       0x20200684   Section        0  icm20608.o(.bss.gyro_offset)
    left_speed_filter_buf                    0x2020073c   Data          24  encoder_driver.o(.bss.left_speed_filter_buf)
    [Anonymous Symbol]                       0x2020073c   Section        0  encoder_driver.o(.bss.left_speed_filter_buf)
    right_speed_filter_buf                   0x2020075c   Data          24  encoder_driver.o(.bss.right_speed_filter_buf)
    [Anonymous Symbol]                       0x2020075c   Section        0  encoder_driver.o(.bss.right_speed_filter_buf)
    rotation                                 0x20200774   Data           1  ssd1306.o(.bss.rotation)
    [Anonymous Symbol]                       0x20200774   Section        0  ssd1306.o(.bss.rotation)
    save_timer                               0x20200778   Data           4  calibration.o(.bss.save_timer)
    [Anonymous Symbol]                       0x20200778   Section        0  calibration.o(.bss.save_timer)
    speed_filter_param                       0x2020077c   Data          24  encoder_driver.o(.bss.speed_filter_param)
    [Anonymous Symbol]                       0x2020077c   Section        0  encoder_driver.o(.bss.speed_filter_param)
    temp_history                             0x202007d8   Data          40  icm20608.o(.bss.temp_history)
    [Anonymous Symbol]                       0x202007d8   Section        0  icm20608.o(.bss.temp_history)
    temp_history_full                        0x20200800   Data           1  icm20608.o(.bss.temp_history_full)
    [Anonymous Symbol]                       0x20200800   Section        0  icm20608.o(.bss.temp_history_full)
    temp_history_index                       0x20200801   Data           1  icm20608.o(.bss.temp_history_index)
    [Anonymous Symbol]                       0x20200801   Section        0  icm20608.o(.bss.temp_history_index)
    temp_stable_timer                        0x20200804   Data           4  icm20608.o(.bss.temp_stable_timer)
    [Anonymous Symbol]                       0x20200804   Section        0  icm20608.o(.bss.temp_stable_timer)
    temperature_stable_flag                  0x20200808   Data           1  icm20608.o(.bss.temperature_stable_flag)
    [Anonymous Symbol]                       0x20200808   Section        0  icm20608.o(.bss.temperature_stable_flag)
    textbgcolor                              0x2020080a   Data           2  ssd1306.o(.bss.textbgcolor)
    [Anonymous Symbol]                       0x2020080a   Section        0  ssd1306.o(.bss.textbgcolor)
    textcolor                                0x2020080c   Data           2  ssd1306.o(.bss.textcolor)
    [Anonymous Symbol]                       0x2020080c   Section        0  ssd1306.o(.bss.textcolor)
    textsize                                 0x2020080e   Data           1  ssd1306.o(.bss.textsize)
    [Anonymous Symbol]                       0x2020080e   Section        0  ssd1306.o(.bss.textsize)
    uart0_rx_buffer                          0x20200810   Data         128  uart_app.o(.bss.uart0_rx_buffer)
    [Anonymous Symbol]                       0x20200810   Section        0  uart_app.o(.bss.uart0_rx_buffer)
    uart0_rx_complete                        0x20200890   Data           1  uart_app.o(.bss.uart0_rx_complete)
    [Anonymous Symbol]                       0x20200890   Section        0  uart_app.o(.bss.uart0_rx_complete)
    uart0_rx_index                           0x20200892   Data           2  uart_app.o(.bss.uart0_rx_index)
    [Anonymous Symbol]                       0x20200892   Section        0  uart_app.o(.bss.uart0_rx_index)
    uart0_rx_tick                            0x20200894   Data           4  uart_app.o(.bss.uart0_rx_tick)
    [Anonymous Symbol]                       0x20200894   Section        0  uart_app.o(.bss.uart0_rx_tick)
    yaw_drift_rate                           0x202008ac   Data           4  icm20608.o(.bss.yaw_drift_rate)
    [Anonymous Symbol]                       0x202008ac   Section        0  icm20608.o(.bss.yaw_drift_rate)
    yaw_history                              0x202008b0   Data          80  icm20608.o(.bss.yaw_history)
    [Anonymous Symbol]                       0x202008b0   Section        0  icm20608.o(.bss.yaw_history)
    yaw_history_full                         0x20200900   Data           1  icm20608.o(.bss.yaw_history_full)
    [Anonymous Symbol]                       0x20200900   Section        0  icm20608.o(.bss.yaw_history_full)
    yaw_history_index                        0x20200901   Data           1  icm20608.o(.bss.yaw_history_index)
    [Anonymous Symbol]                       0x20200901   Section        0  icm20608.o(.bss.yaw_history_index)
    yaw_lpf_output                           0x20200904   Data           4  icm20608.o(.bss.yaw_lpf_output)
    [Anonymous Symbol]                       0x20200904   Section        0  icm20608.o(.bss.yaw_lpf_output)
    yaw_reset_request                        0x20200908   Data           1  icm20608.o(.bss.yaw_reset_request)
    [Anonymous Symbol]                       0x20200908   Section        0  icm20608.o(.bss.yaw_reset_request)
    Heap_Mem                                 0x20200910   Data        4096  startup_mspm0g350x_uvision.o(HEAP)
    HEAP                                     0x20200910   Section     4096  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20201910   Data        8192  startup_mspm0g350x_uvision.o(STACK)
    STACK                                    0x20201910   Section     8192  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x20203910   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x00000121   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x00000141   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000149   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x00000165   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_u                                0x00000167   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_percent_end                      0x00000171   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x00000175   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000179   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000017d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000017d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000017d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000183   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000183   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000187   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000187   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000018f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x00000191   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x00000191   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000195   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    __aeabi_memcpy4                          0x0000019d   Thumb Code    56  rt_memcpy.o(.emb_text)
    __aeabi_memcpy8                          0x0000019d   Thumb Code     0  rt_memcpy.o(.emb_text)
    Reset_Handler                            0x000001d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000001dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000001df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000001e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000001e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x000001e5   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    _printf_int_dec                          0x00000205   Thumb Code    90  _printf_dec.o(.text)
    __aeabi_memcpy                           0x00000271   Thumb Code   130  rt_memcpy.o(.text)
    __rt_memcpy                              0x00000271   Thumb Code     0  rt_memcpy.o(.text)
    _memset_w                                0x000002f3   Thumb Code    26  rt_memclr.o(.text)
    _memset                                  0x0000030d   Thumb Code    30  rt_memclr.o(.text)
    __aeabi_memclr                           0x0000032b   Thumb Code     4  rt_memclr.o(.text)
    __rt_memclr                              0x0000032b   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x0000032f   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr8                          0x0000032f   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr_w                            0x0000032f   Thumb Code     4  rt_memclr.o(.text)
    __aeabi_uidivmod                         0x00000335   Thumb Code    28  aeabi_sdivfast.o(.text)
    __aeabi_idivmod                          0x00000351   Thumb Code   472  aeabi_sdivfast.o(.text)
    __use_two_region_memory                  0x0000052d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0000052f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x00000531   Thumb Code     2  heapauxi.o(.text)
    __aeabi_fdiv                             0x00000535   Thumb Code     0  fdiv.o(.text)
    _fdiv                                    0x00000535   Thumb Code   334  fdiv.o(.text)
    _frdiv                                   0x00000683   Thumb Code     8  fdiv.o(.text)
    __aeabi_f2iz                             0x00000695   Thumb Code     0  ffixi.o(.text)
    _ffix                                    0x00000695   Thumb Code    76  ffixi.o(.text)
    __aeabi_f2uiz                            0x000006e1   Thumb Code     0  ffixui.o(.text)
    _ffixu                                   0x000006e1   Thumb Code    48  ffixui.o(.text)
    __aeabi_i2f_normalise                    0x00000711   Thumb Code    72  fflti.o(.text)
    __aeabi_i2f                              0x00000759   Thumb Code    16  fflti.o(.text)
    _fflt                                    0x00000759   Thumb Code     0  fflti.o(.text)
    __aeabi_ui2f                             0x00000769   Thumb Code     6  fflti.o(.text)
    _ffltu                                   0x00000769   Thumb Code     0  fflti.o(.text)
    __read_errno                             0x0000076f   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x00000779   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x00000785   Thumb Code   176  _printf_intcommon.o(.text)
    __rt_udiv10                              0x00000835   Thumb Code    40  rtudiv10.o(.text)
    _frnd                                    0x0000085d   Thumb Code   122  frnd.o(.text)
    _fsqrt                                   0x000008dd   Thumb Code   140  fsqrt.o(.text)
    __aeabi_errno_addr                       0x0000096d   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x0000096d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x0000096d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __fpl_fcmp_InfNaN                        0x00000975   Thumb Code    96  fcmpin.o(.text)
    __ARM_scalbnf                            0x000009d9   Thumb Code    64  fscalbn.o(.text)
    __user_libspace                          0x00000a1d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x00000a1d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x00000a1d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x00000a25   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x00000a63   Thumb Code    16  exit.o(.text)
    __fpl_cmpreturn                          0x00000a73   Thumb Code    46  cmpret.o(.text)
    __fpl_fcheck_NaN2                        0x00000aa1   Thumb Code    10  fnan2.o(.text)
    __fpl_return_NaN                         0x00000ab1   Thumb Code    94  retnan.o(.text)
    _sys_exit                                0x00000b11   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x00000b1d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x00000b1d   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x00000b1f   Thumb Code     0  indicate_semi.o(.text)
    Button_Init                              0x00000b21   Thumb Code   104  nbutton.o(.text.Button_Init)
    Buzzer_Beep                              0x00000b91   Thumb Code    20  buzzer_app.o(.text.Buzzer_Beep)
    Buzzer_Init                              0x00000ba5   Thumb Code    40  buzzer_app.o(.text.Buzzer_Init)
    Buzzer_Work                              0x00000bd9   Thumb Code   136  buzzer_app.o(.text.Buzzer_Work)
    Calibration_Get_CRC32                    0x00000c65   Thumb Code   248  calibration.o(.text.Calibration_Get_CRC32)
    Calibration_Get_Status                   0x00000d5d   Thumb Code     8  calibration.o(.text.Calibration_Get_Status)
    Calibration_Save                         0x00000d65   Thumb Code   276  calibration.o(.text.Calibration_Save)
    Calibration_Set_Gyro_Offset              0x00000e79   Thumb Code    16  calibration.o(.text.Calibration_Set_Gyro_Offset)
    Calibration_Update                       0x00000e89   Thumb Code   316  calibration.o(.text.Calibration_Update)
    Calibration_Verify_Integrity             0x00000fe1   Thumb Code   256  calibration.o(.text.Calibration_Verify_Integrity)
    DL_ADC12_setClockConfig                  0x000010e9   Thumb Code    56  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x00001129   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_DMA_initChannel                       0x00001135   Thumb Code    64  dl_dma.o(.text.DL_DMA_initChannel)
    DL_I2C_fillControllerTXFIFO              0x00001179   Thumb Code   128  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    DL_I2C_flushControllerTXFIFO             0x000011f9   Thumb Code    64  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    DL_I2C_setClockConfig                    0x0000123d   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_SPI_init                              0x00001265   Thumb Code    60  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_setClockConfig                    0x000012a9   Thumb Code    18  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_SYSCTL_configSYSPLL                   0x000012bd   Thumb Code   220  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x000013a9   Thumb Code   100  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00001411   Thumb Code    72  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_TimerA_initPWMMode                    0x00001461   Thumb Code   108  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_initPWMMode                     0x000014d1   Thumb Code   172  dl_timer.o(.text.DL_Timer_initPWMMode)
    DL_Timer_initTimerMode                   0x00001591   Thumb Code   220  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00001679   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00001695   Thumb Code    20  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x000016ad   Thumb Code    12  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x000016bd   Thumb Code    24  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x000016d9   Thumb Code    64  dl_uart.o(.text.DL_UART_init)
    DL_UART_receiveDataCheck                 0x00001721   Thumb Code    24  dl_uart.o(.text.DL_UART_receiveDataCheck)
    DL_UART_setClockConfig                   0x00001739   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_transmitDataBlocking             0x0000174d   Thumb Code    32  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    Delay_Ms                                 0x00001771   Thumb Code   176  system.o(.text.Delay_Ms)
    Draw_Logo                                0x0000182d   Thumb Code   192  oled.o(.text.Draw_Logo)
    Encoder_Init                             0x000018f9   Thumb Code    12  encoder_driver.o(.text.Encoder_Init)
    FusionOffsetUpdate                       0x0000190d   Thumb Code   192  fusionoffset.o(.text.FusionOffsetUpdate)
    GROUP1_IRQHandler                        0x000019d5   Thumb Code    68  interrupt_handler.o(.text.GROUP1_IRQHandler)
    HUI_Get_Pin_State                        0x00001a21   Thumb Code    16  hui_app.o(.text.HUI_Get_Pin_State)
    HUI_Read_All_Pins                        0x00001a31   Thumb Code   104  hui_app.o(.text.HUI_Read_All_Pins)
    HardFault_Handler                        0x00001aa5   Thumb Code    12  system.o(.text.HardFault_Handler)
    I2C_ReadReg                              0x00001ab5   Thumb Code   664  ni2c.o(.text.I2C_ReadReg)
    ICM20608_Calibrate_Gyro                  0x00001d61   Thumb Code   324  icm20608.o(.text.ICM20608_Calibrate_Gyro)
    ICM20608_Get_Angles                      0x00001ead   Thumb Code    16  icm20608.o(.text.ICM20608_Get_Angles)
    ICM20608_Get_Calib_Status                0x00001ec1   Thumb Code    24  icm20608.o(.text.ICM20608_Get_Calib_Status)
    ICM20608_Get_Calibration_Status          0x00001ee1   Thumb Code     8  icm20608.o(.text.ICM20608_Get_Calibration_Status)
    ICM20608_Get_Fusion_Status               0x00001ee9   Thumb Code     8  icm20608.o(.text.ICM20608_Get_Fusion_Status)
    ICM20608_Get_Fusion_Timer                0x00001ef1   Thumb Code    16  icm20608.o(.text.ICM20608_Get_Fusion_Timer)
    ICM20608_Get_Gyro_Offset                 0x00001f01   Thumb Code    40  icm20608.o(.text.ICM20608_Get_Gyro_Offset)
    ICM20608_Get_Temperature_Range           0x00001f35   Thumb Code   356  icm20608.o(.text.ICM20608_Get_Temperature_Range)
    ICM20608_Get_Yaw_Drift_Rate              0x000020a5   Thumb Code     8  icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate)
    ICM20608_Get_Yaw_Quality                 0x000020b1   Thumb Code     8  icm20608.o(.text.ICM20608_Get_Yaw_Quality)
    ICM20608_Is_Temperature_Stable           0x000020bd   Thumb Code     8  icm20608.o(.text.ICM20608_Is_Temperature_Stable)
    ICM20608_Read_Data                       0x000020c5   Thumb Code   880  icm20608.o(.text.ICM20608_Read_Data)
    ICM20608_Update_Angles                   0x0000247d   Thumb Code   908  icm20608.o(.text.ICM20608_Update_Angles)
    IMU_App_Get_Angles                       0x00002875   Thumb Code    16  imu_app.o(.text.IMU_App_Get_Angles)
    IMU_App_Get_Calib_Status                 0x00002885   Thumb Code     8  imu_app.o(.text.IMU_App_Get_Calib_Status)
    IMU_App_Get_Calibration_Status           0x0000288d   Thumb Code     8  imu_app.o(.text.IMU_App_Get_Calibration_Status)
    IMU_App_Get_Calibration_Status_String    0x00002895   Thumb Code    24  imu_app.o(.text.IMU_App_Get_Calibration_Status_String)
    IMU_App_Get_Fusion_Status                0x000028cd   Thumb Code     8  imu_app.o(.text.IMU_App_Get_Fusion_Status)
    IMU_App_Get_Fusion_Timer                 0x000028d5   Thumb Code     8  imu_app.o(.text.IMU_App_Get_Fusion_Timer)
    IMU_App_Get_Gyro_Offset                  0x000028dd   Thumb Code     8  imu_app.o(.text.IMU_App_Get_Gyro_Offset)
    IMU_App_Get_Performance_Stats            0x000028e5   Thumb Code    60  imu_app.o(.text.IMU_App_Get_Performance_Stats)
    IMU_App_Get_System_Status                0x00002921   Thumb Code    32  imu_app.o(.text.IMU_App_Get_System_Status)
    IMU_App_Get_Temperature                  0x00002941   Thumb Code     8  imu_app.o(.text.IMU_App_Get_Temperature)
    IMU_App_Get_Temperature_Range            0x00002949   Thumb Code     8  imu_app.o(.text.IMU_App_Get_Temperature_Range)
    IMU_App_Get_Test_Results                 0x00002951   Thumb Code    80  imu_app.o(.text.IMU_App_Get_Test_Results)
    IMU_App_Get_Test_Status_String           0x000029a1   Thumb Code    84  imu_app.o(.text.IMU_App_Get_Test_Status_String)
    IMU_App_Get_Yaw_Drift_Rate               0x00002a0d   Thumb Code     8  imu_app.o(.text.IMU_App_Get_Yaw_Drift_Rate)
    IMU_App_Get_Yaw_Quality                  0x00002a15   Thumb Code     8  imu_app.o(.text.IMU_App_Get_Yaw_Quality)
    IMU_App_Get_Yaw_Status_String            0x00002a1d   Thumb Code    44  imu_app.o(.text.IMU_App_Get_Yaw_Status_String)
    IMU_App_Is_Ready                         0x00002a71   Thumb Code    20  imu_app.o(.text.IMU_App_Is_Ready)
    IMU_App_Is_Stationary                    0x00002a85   Thumb Code    18  imu_app.o(.text.IMU_App_Is_Stationary)
    IMU_App_Is_Temperature_Stable            0x00002a97   Thumb Code     8  imu_app.o(.text.IMU_App_Is_Temperature_Stable)
    IMU_App_Update                           0x00002aa1   Thumb Code    96  imu_app.o(.text.IMU_App_Update)
    LCD_CLS                                  0x00002b0d   Thumb Code   364  oled.o(.text.LCD_CLS)
    LCD_P6x8Char                             0x00002c79   Thumb Code    96  oled.o(.text.LCD_P6x8Char)
    LCD_P6x8Str                              0x00002cd9   Thumb Code   136  oled.o(.text.LCD_P6x8Str)
    LCD_clear_L                              0x00002d65   Thumb Code   232  oled.o(.text.LCD_clear_L)
    LED_App_Init                             0x00002e51   Thumb Code    20  led_app.o(.text.LED_App_Init)
    LPButterworth                            0x00002e69   Thumb Code   324  filter.o(.text.LPButterworth)
    Motor_App_Init                           0x00002fb1   Thumb Code    48  motor_app.o(.text.Motor_App_Init)
    NMI_Handler                              0x00002fe5   Thumb Code    12  system.o(.text.NMI_Handler)
    OLED_Init                                0x00002ff5   Thumb Code    40  oled.o(.text.OLED_Init)
    OLED_WrCmd                               0x00003021   Thumb Code   132  oled.o(.text.OLED_WrCmd)
    OLED_WrDat                               0x000030a5   Thumb Code   132  oled.o(.text.OLED_WrDat)
    QEI0_IRQHandler                          0x00003131   Thumb Code    60  encoder_driver.o(.text.QEI0_IRQHandler)
    QEI1_IRQHandler                          0x0000316d   Thumb Code    60  encoder_driver.o(.text.QEI1_IRQHandler)
    Read_Button_State_One                    0x000031b5   Thumb Code   204  nbutton.o(.text.Read_Button_State_One)
    SYSCFG_DL_ADC12_0_init                   0x00003289   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    SYSCFG_DL_DMA_CH0_init                   0x000032ed   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    SYSCFG_DL_DMA_init                       0x00003305   Thumb Code     8  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    SYSCFG_DL_GPIO_init                      0x0000330d   Thumb Code   284  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_I2C_0_init                     0x00003459   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    SYSCFG_DL_PWM_0_init                     0x000034ad   Thumb Code   184  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    SYSCFG_DL_PWM_1_init                     0x00003575   Thumb Code   108  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init)
    SYSCFG_DL_PWM_2_init                     0x000035f1   Thumb Code   108  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init)
    SYSCFG_DL_SPI_0_init                     0x0000366d   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init)
    SYSCFG_DL_SYSCTL_CLK_init                0x000036b9   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    SYSCFG_DL_SYSCTL_init                    0x000036e9   Thumb Code   144  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00003785   Thumb Code    36  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_G0_init                  0x000037b5   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init)
    SYSCFG_DL_TIMER_G12_init                 0x000037fd   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init)
    SYSCFG_DL_TIMER_G6_init                  0x00003849   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init)
    SYSCFG_DL_TIMER_G8_init                  0x00003899   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init)
    SYSCFG_DL_UART_0_init                    0x000038e5   Thumb Code   104  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_1_init                    0x00003961   Thumb Code   100  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    SYSCFG_DL_UART_2_init                    0x000039d9   Thumb Code   136  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    SYSCFG_DL_UART_3_init                    0x00003a79   Thumb Code   108  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    SYSCFG_DL_init                           0x00003b01   Thumb Code   120  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00003b91   Thumb Code   136  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Handler                          0x00003c61   Thumb Code    12  system.o(.text.SysTick_Handler)
    TIMG0_IRQHandler                         0x00003c6d   Thumb Code    20  ntimer.o(.text.TIMG0_IRQHandler)
    TIMG12_IRQHandler                        0x00003c89   Thumb Code    24  ntimer.o(.text.TIMG12_IRQHandler)
    TIMG6_IRQHandler                         0x00003ca9   Thumb Code    28  ntimer.o(.text.TIMG6_IRQHandler)
    TIMG8_IRQHandler                         0x00003cd1   Thumb Code    20  ntimer.o(.text.TIMG8_IRQHandler)
    UART0_App_GetRxData                      0x00003ced   Thumb Code    60  uart_app.o(.text.UART0_App_GetRxData)
    UART0_App_Init                           0x00003d29   Thumb Code    28  uart_app.o(.text.UART0_App_Init)
    UART0_App_IsRxComplete                   0x00003d4d   Thumb Code     8  uart_app.o(.text.UART0_App_IsRxComplete)
    UART0_App_RxCallback                     0x00003d55   Thumb Code    56  uart_app.o(.text.UART0_App_RxCallback)
    UART0_App_RxProcess                      0x00003d91   Thumb Code    32  uart_app.o(.text.UART0_App_RxProcess)
    UART0_App_SendBytes                      0x00003dbd   Thumb Code    28  uart_app.o(.text.UART0_App_SendBytes)
    UART0_IRQHandler                         0x00003dd9   Thumb Code    52  nuart.o(.text.UART0_IRQHandler)
    UART1_IRQHandler                         0x00003e15   Thumb Code    20  nuart.o(.text.UART1_IRQHandler)
    UART2_IRQHandler                         0x00003e31   Thumb Code    36  nuart.o(.text.UART2_IRQHandler)
    UART3_IRQHandler                         0x00003e61   Thumb Code    20  nuart.o(.text.UART3_IRQHandler)
    delay_ms                                 0x00003e7d   Thumb Code   176  system.o(.text.delay_ms)
    display_6_8_number                       0x00003f31   Thumb Code     8  oled.o(.text.display_6_8_number)
    display_6_8_string                       0x00003f39   Thumb Code     8  oled.o(.text.display_6_8_string)
    get_key_short_press                      0x00003f41   Thumb Code    80  nbutton.o(.text.get_key_short_press)
    get_left_motor_speed                     0x00003f91   Thumb Code   160  encoder_driver.o(.text.get_left_motor_speed)
    get_right_motor_speed                    0x00004049   Thumb Code   124  encoder_driver.o(.text.get_right_motor_speed)
    get_systime                              0x000040e9   Thumb Code    76  system.o(.text.get_systime)
    get_wheel_speed                          0x00004139   Thumb Code    12  encoder_driver.o(.text.get_wheel_speed)
    i2creadnbyte                             0x00004145   Thumb Code     8  ni2c.o(.text.i2creadnbyte)
    main                                     0x0000414d   Thumb Code    58  main.o(.text.main)
    millis                                   0x00004189   Thumb Code    36  system.o(.text.millis)
    ncontroller_set_priority                 0x000041b9   Thumb Code   108  system.o(.text.ncontroller_set_priority)
    read_button_state_all                    0x00004231   Thumb Code    80  nbutton.o(.text.read_button_state_all)
    screen_display                           0x00004285   Thumb Code  3660  ui.o(.text.screen_display)
    set_cutoff_frequency                     0x0000517d   Thumb Code   168  filter.o(.text.set_cutoff_frequency)
    ssd1306_begin                            0x00005235   Thumb Code   264  ssd1306.o(.text.ssd1306_begin)
    timer_irq_config                         0x00005371   Thumb Code    80  ntimer.o(.text.timer_irq_config)
    uart_process                             0x000053dd   Thumb Code    42  uart_process.o(.text.uart_process)
    usart_irq_config                         0x00005409   Thumb Code    64  nuart.o(.text.usart_irq_config)
    write_6_8_number                         0x00005465   Thumb Code   676  oled.o(.text.write_6_8_number)
    __aeabi_uidiv                            0x00005749   Thumb Code    68  aeabi_sdivfast.o(.text_divfast)
    __aeabi_idiv                             0x0000578d   Thumb Code   434  aeabi_sdivfast.o(.text_divfast)
    __ARM_common_ll_muluu                    0x0000593f   Thumb Code    50  rredf.o(i.__ARM_common_ll_muluu)
    __ARM_fpclassifyf                        0x00005971   Thumb Code    34  fpclassifyf.o(i.__ARM_fpclassifyf)
    __mathlib_flt_infnan                     0x00005993   Thumb Code    10  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x0000599d   Thumb Code     8  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x000059a5   Thumb Code    12  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x000059b1   Thumb Code    14  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x000059c1   Thumb Code   366  rredf.o(i.__mathlib_rredf2)
    __aeabi_fcmpeq                           0x00005b41   Thumb Code     0  fcmp.o(i._feq)
    _feq                                     0x00005b41   Thumb Code    22  fcmp.o(i._feq)
    __aeabi_fcmpge                           0x00005b57   Thumb Code     0  fcmp.o(i._fgeq)
    _fgeq                                    0x00005b57   Thumb Code    22  fcmp.o(i._fgeq)
    __aeabi_fcmpgt                           0x00005b6d   Thumb Code     0  fcmp.o(i._fgr)
    _fgr                                     0x00005b6d   Thumb Code    22  fcmp.o(i._fgr)
    __aeabi_fcmple                           0x00005b83   Thumb Code     0  fcmp.o(i._fleq)
    _fleq                                    0x00005b83   Thumb Code    26  fcmp.o(i._fleq)
    __aeabi_fcmplt                           0x00005b9d   Thumb Code     0  fcmp.o(i._fls)
    _fls                                     0x00005b9d   Thumb Code    22  fcmp.o(i._fls)
    atan2f                                   0x00005bb5   Thumb Code   592  atan2f.o(i.atan2f)
    sqrtf                                    0x00005e51   Thumb Code    44  sqrtf.o(i.sqrtf)
    tanf                                     0x00005e7d   Thumb Code   312  tanf.o(i.tanf)
    __aeabi_fadd                             0x00005fed   Thumb Code     0  faddsub.o(x$fpl$fadd)
    _fadd                                    0x00005fed   Thumb Code   134  faddsub.o(x$fpl$fadd)
    __aeabi_cfcmpeq                          0x00006079   Thumb Code     0  feqf.o(x$fpl$feqf)
    _fcmpeq                                  0x00006079   Thumb Code    78  feqf.o(x$fpl$feqf)
    _fcmpge                                  0x000060cd   Thumb Code    78  fgef.o(x$fpl$fgeqf)
    __aeabi_cfcmple                          0x00006121   Thumb Code     0  flef.o(x$fpl$fleqf)
    _fcmple                                  0x00006121   Thumb Code    78  flef.o(x$fpl$fleqf)
    __aeabi_fmul                             0x00006175   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x00006175   Thumb Code   172  fmul.o(x$fpl$fmul)
    __aeabi_frsub                            0x00006225   Thumb Code     0  faddsub.o(x$fpl$frsb)
    _frsb                                    0x00006225   Thumb Code    24  faddsub.o(x$fpl$frsb)
    __aeabi_fsub                             0x0000623d   Thumb Code     0  faddsub.o(x$fpl$fsub)
    _fsub                                    0x0000623d   Thumb Code   204  faddsub.o(x$fpl$fsub)
    __I$use$fp                               0x0000630c   Number         0  usenofp.o(x$fpl$usenofp)
    F6x8                                     0x0000636c   Data         552  oled.o(.rodata.F6x8)
    NC_Logo                                  0x00006594   Data        1024  oled.o(.rodata.NC_Logo)
    Region$$Table$$Base                      0x00006f10   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00006f30   Number         0  anon$$obj.o(Region$$Table)
    NEncoder                                 0x20200000   Data          60  encoder_driver.o(.data.NEncoder)
    trackless_motor                          0x20200040   Data          16  encoder_driver.o(.data.trackless_motor)
    __libspace_start                         0x20200058   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x202000b8   Data           0  libspace.o(.bss)
    _button                                  0x202000c4   Data         288  nbutton.o(.bss._button)
    _cp437                                   0x202001e4   Data           1  ssd1306.o(.bss._cp437)
    buzzer                                   0x20200280   Data          28  buzzer_app.o(.bss.buzzer)
    gPWM_0Backup                             0x2020032c   Data         188  ti_msp_dl_config.o(.bss.gPWM_0Backup)
    gPWM_1Backup                             0x202003e8   Data         188  ti_msp_dl_config.o(.bss.gPWM_1Backup)
    gPWM_2Backup                             0x202004a4   Data         120  ti_msp_dl_config.o(.bss.gPWM_2Backup)
    gSPI_0Backup                             0x2020051c   Data          40  ti_msp_dl_config.o(.bss.gSPI_0Backup)
    gTIMER_G6Backup                          0x20200544   Data         120  ti_msp_dl_config.o(.bss.gTIMER_G6Backup)
    gUART_3Backup                            0x202005bc   Data          48  ti_msp_dl_config.o(.bss.gUART_3Backup)
    hf_cnt                                   0x20200690   Data           4  system.o(.bss.hf_cnt)
    hui_pin_states                           0x20200694   Data          12  hui_app.o(.bss.hui_pin_states)
    imu_data                                 0x202006a0   Data          68  icm20608.o(.bss.imu_data)
    imu_status                               0x202006e4   Data          24  imu_app.o(.bss.imu_status)
    irq_poriority                            0x202006fc   Data          64  system.o(.bss.irq_poriority)
    nmi_cnt                                  0x20200754   Data           4  system.o(.bss.nmi_cnt)
    page_number                              0x20200758   Data           2  ui.o(.bss.page_number)
    sysTickUptime                            0x20200794   Data           4  system.o(.bss.sysTickUptime)
    t_g0                                     0x20200798   Data          16  ntimer.o(.bss.t_g0)
    t_g1                                     0x202007a8   Data          16  ntimer.o(.bss.t_g1)
    t_g12                                    0x202007b8   Data          16  ntimer.o(.bss.t_g12)
    t_g8                                     0x202007c8   Data          16  ntimer.o(.bss.t_g8)
    uart2_dma_t                              0x20200898   Data          16  nuart.o(.bss.uart2_dma_t)
    wrap                                     0x202008a8   Data           1  ssd1306.o(.bss.wrap)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00006f88, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00006f30, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO          776    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO         1715  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO         1993    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x0000001a   Code   RO         1997    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0000013a   0x0000013a   0x00000006   PAD
    0x00000140   0x00000140   0x00000002   Code   RO         1994    !!handler_null      c_p.l(__scatter.o)
    0x00000142   0x00000142   0x00000006   PAD
    0x00000148   0x00000148   0x0000001c   Code   RO         1999    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000164   0x00000164   0x00000002   Code   RO         1698    .ARM.Collect$$_printf_percent$$00000000  c_p.l(_printf_percent.o)
    0x00000166   0x00000166   0x0000000a   Code   RO         1697    .ARM.Collect$$_printf_percent$$0000000A  c_p.l(_printf_u.o)
    0x00000170   0x00000170   0x00000004   Code   RO         1771    .ARM.Collect$$_printf_percent$$00000017  c_p.l(_printf_percent_end.o)
    0x00000174   0x00000174   0x00000002   Code   RO         1851    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1866    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1868    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1870    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1873    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1875    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1877    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1880    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1882    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1884    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1886    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1888    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1890    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1892    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1894    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1896    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1898    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1900    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1904    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1906    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1908    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1910    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000002   Code   RO         1911    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000178   0x00000178   0x00000002   Code   RO         1948    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO         1976    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO         1978    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO         1981    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO         1984    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO         1986    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO         1989    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000002   Code   RO         1990    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000017c   0x0000017c   0x00000000   Code   RO         1758    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000017c   0x0000017c   0x00000000   Code   RO         1807    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000017c   0x0000017c   0x00000006   Code   RO         1819    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000182   0x00000182   0x00000000   Code   RO         1809    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000182   0x00000182   0x00000004   Code   RO         1810    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000186   0x00000186   0x00000000   Code   RO         1812    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000186   0x00000186   0x00000008   Code   RO         1813    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000018e   0x0000018e   0x00000002   Code   RO         1857    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x00000190   0x00000190   0x00000000   Code   RO         1919    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x00000190   0x00000190   0x00000004   Code   RO         1920    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000194   0x00000194   0x00000006   Code   RO         1921    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0000019a   0x0000019a   0x00000002   PAD
    0x0000019c   0x0000019c   0x00000038   Code   RO         1699    .emb_text           c_p.l(rt_memcpy.o)
    0x000001d4   0x000001d4   0x00000030   Code   RO          777    .text               startup_mspm0g350x_uvision.o
    0x00000204   0x00000204   0x0000006c   Code   RO         1675    .text               c_p.l(_printf_dec.o)
    0x00000270   0x00000270   0x00000082   Code   RO         1700    .text               c_p.l(rt_memcpy.o)
    0x000002f2   0x000002f2   0x00000040   Code   RO         1703    .text               c_p.l(rt_memclr.o)
    0x00000332   0x00000332   0x00000002   PAD
    0x00000334   0x00000334   0x000001f8   Code   RO         1705    .text               c_p.l(aeabi_sdivfast.o)
    0x0000052c   0x0000052c   0x00000006   Code   RO         1713    .text               c_p.l(heapauxi.o)
    0x00000532   0x00000532   0x00000002   PAD
    0x00000534   0x00000534   0x00000160   Code   RO         1735    .text               fz_ps.l(fdiv.o)
    0x00000694   0x00000694   0x0000004c   Code   RO         1738    .text               fz_ps.l(ffixi.o)
    0x000006e0   0x000006e0   0x00000030   Code   RO         1740    .text               fz_ps.l(ffixui.o)
    0x00000710   0x00000710   0x0000005e   Code   RO         1742    .text               fz_ps.l(fflti.o)
    0x0000076e   0x0000076e   0x00000016   Code   RO         1763    .text               c_p.l(_rserrno.o)
    0x00000784   0x00000784   0x000000b0   Code   RO         1765    .text               c_p.l(_printf_intcommon.o)
    0x00000834   0x00000834   0x00000028   Code   RO         1772    .text               c_p.l(rtudiv10.o)
    0x0000085c   0x0000085c   0x00000080   Code   RO         1780    .text               fz_ps.l(frnd.o)
    0x000008dc   0x000008dc   0x00000090   Code   RO         1782    .text               fz_ps.l(fsqrt.o)
    0x0000096c   0x0000096c   0x00000008   Code   RO         1826    .text               c_p.l(rt_errno_addr_intlibspace.o)
    0x00000974   0x00000974   0x00000064   Code   RO         1830    .text               fz_ps.l(fcmpin.o)
    0x000009d8   0x000009d8   0x00000044   Code   RO         1832    .text               fz_ps.l(fscalbn.o)
    0x00000a1c   0x00000a1c   0x00000008   Code   RO         1834    .text               c_p.l(libspace.o)
    0x00000a24   0x00000a24   0x0000003e   Code   RO         1837    .text               c_p.l(sys_stackheap_outer.o)
    0x00000a62   0x00000a62   0x00000010   Code   RO         1840    .text               c_p.l(exit.o)
    0x00000a72   0x00000a72   0x0000002e   Code   RO         1852    .text               fz_ps.l(cmpret.o)
    0x00000aa0   0x00000aa0   0x00000010   Code   RO         1854    .text               fz_ps.l(fnan2.o)
    0x00000ab0   0x00000ab0   0x0000005e   Code   RO         1912    .text               fz_ps.l(retnan.o)
    0x00000b0e   0x00000b0e   0x00000002   PAD
    0x00000b10   0x00000b10   0x0000000c   Code   RO         1914    .text               c_p.l(sys_exit.o)
    0x00000b1c   0x00000b1c   0x00000002   Code   RO         1937    .text               c_p.l(use_no_semi.o)
    0x00000b1e   0x00000b1e   0x00000000   Code   RO         1939    .text               c_p.l(indicate_semi.o)
    0x00000b1e   0x00000b1e   0x00000002   PAD
    0x00000b20   0x00000b20   0x00000070   Code   RO          935    .text.Button_Init   nbutton.o
    0x00000b90   0x00000b90   0x00000014   Code   RO          972    .text.Buzzer_Beep   buzzer_app.o
    0x00000ba4   0x00000ba4   0x00000034   Code   RO          958    .text.Buzzer_Init   buzzer_app.o
    0x00000bd8   0x00000bd8   0x0000008c   Code   RO          960    .text.Buzzer_Work   buzzer_app.o
    0x00000c64   0x00000c64   0x000000f8   Code   RO         1624    .text.Calibration_Get_CRC32  calibration.o
    0x00000d5c   0x00000d5c   0x00000008   Code   RO         1606    .text.Calibration_Get_Status  calibration.o
    0x00000d64   0x00000d64   0x00000114   Code   RO         1602    .text.Calibration_Save  calibration.o
    0x00000e78   0x00000e78   0x00000010   Code   RO         1608    .text.Calibration_Set_Gyro_Offset  calibration.o
    0x00000e88   0x00000e88   0x00000158   Code   RO         1622    .text.Calibration_Update  calibration.o
    0x00000fe0   0x00000fe0   0x00000108   Code   RO         1626    .text.Calibration_Verify_Integrity  calibration.o
    0x000010e8   0x000010e8   0x00000040   Code   RO            2    .text.DL_ADC12_setClockConfig  dl_adc12.o
    0x00001128   0x00001128   0x0000000a   Code   RO           51    .text.DL_Common_delayCycles  dl_common.o
    0x00001132   0x00001132   0x00000002   PAD
    0x00001134   0x00001134   0x00000044   Code   RO           97    .text.DL_DMA_initChannel  dl_dma.o
    0x00001178   0x00001178   0x00000080   Code   RO          271    .text.DL_I2C_fillControllerTXFIFO  dl_i2c.o
    0x000011f8   0x000011f8   0x00000044   Code   RO          273    .text.DL_I2C_flushControllerTXFIFO  dl_i2c.o
    0x0000123c   0x0000123c   0x00000026   Code   RO          267    .text.DL_I2C_setClockConfig  dl_i2c.o
    0x00001262   0x00001262   0x00000002   PAD
    0x00001264   0x00001264   0x00000044   Code   RO          511    .text.DL_SPI_init   dl_spi.o
    0x000012a8   0x000012a8   0x00000012   Code   RO          513    .text.DL_SPI_setClockConfig  dl_spi.o
    0x000012ba   0x000012ba   0x00000002   PAD
    0x000012bc   0x000012bc   0x000000ec   Code   RO          743    .text.DL_SYSCTL_configSYSPLL  dl_sysctl_mspm0g1x0x_g3x0x.o
    0x000013a8   0x000013a8   0x00000068   Code   RO          757    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  dl_sysctl_mspm0g1x0x_g3x0x.o
    0x00001410   0x00001410   0x00000050   Code   RO          751    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  dl_sysctl_mspm0g1x0x_g3x0x.o
    0x00001460   0x00001460   0x00000070   Code   RO          643    .text.DL_TimerA_initPWMMode  dl_timer.o
    0x000014d0   0x000014d0   0x000000c0   Code   RO          587    .text.DL_Timer_initPWMMode  dl_timer.o
    0x00001590   0x00001590   0x000000e8   Code   RO          569    .text.DL_Timer_initTimerMode  dl_timer.o
    0x00001678   0x00001678   0x0000001c   Code   RO          617    .text.DL_Timer_setCaptCompUpdateMethod  dl_timer.o
    0x00001694   0x00001694   0x00000018   Code   RO          591    .text.DL_Timer_setCaptureCompareOutCtl  dl_timer.o
    0x000016ac   0x000016ac   0x00000010   Code   RO          571    .text.DL_Timer_setCaptureCompareValue  dl_timer.o
    0x000016bc   0x000016bc   0x0000001c   Code   RO          565    .text.DL_Timer_setClockConfig  dl_timer.o
    0x000016d8   0x000016d8   0x00000048   Code   RO          676    .text.DL_UART_init  dl_uart.o
    0x00001720   0x00001720   0x00000018   Code   RO          692    .text.DL_UART_receiveDataCheck  dl_uart.o
    0x00001738   0x00001738   0x00000012   Code   RO          678    .text.DL_UART_setClockConfig  dl_uart.o
    0x0000174a   0x0000174a   0x00000002   PAD
    0x0000174c   0x0000174c   0x00000024   Code   RO          690    .text.DL_UART_transmitDataBlocking  dl_uart.o
    0x00001770   0x00001770   0x000000bc   Code   RO         1096    .text.Delay_Ms      system.o
    0x0000182c   0x0000182c   0x000000cc   Code   RO         1158    .text.Draw_Logo     oled.o
    0x000018f8   0x000018f8   0x00000014   Code   RO         1458    .text.Encoder_Init  encoder_driver.o
    0x0000190c   0x0000190c   0x000000c8   Code   RO         1588    .text.FusionOffsetUpdate  fusionoffset.o
    0x000019d4   0x000019d4   0x0000004c   Code   RO         1485    .text.GROUP1_IRQHandler  interrupt_handler.o
    0x00001a20   0x00001a20   0x00000010   Code   RO         1069    .text.HUI_Get_Pin_State  hui_app.o
    0x00001a30   0x00001a30   0x00000074   Code   RO         1067    .text.HUI_Read_All_Pins  hui_app.o
    0x00001aa4   0x00001aa4   0x00000010   Code   RO         1108    .text.HardFault_Handler  system.o
    0x00001ab4   0x00001ab4   0x000002ac   Code   RO         1375    .text.I2C_ReadReg   ni2c.o
    0x00001d60   0x00001d60   0x0000014c   Code   RO         1393    .text.ICM20608_Calibrate_Gyro  icm20608.o
    0x00001eac   0x00001eac   0x00000014   Code   RO         1425    .text.ICM20608_Get_Angles  icm20608.o
    0x00001ec0   0x00001ec0   0x00000020   Code   RO         1395    .text.ICM20608_Get_Calib_Status  icm20608.o
    0x00001ee0   0x00001ee0   0x00000008   Code   RO         1419    .text.ICM20608_Get_Calibration_Status  icm20608.o
    0x00001ee8   0x00001ee8   0x00000008   Code   RO         1399    .text.ICM20608_Get_Fusion_Status  icm20608.o
    0x00001ef0   0x00001ef0   0x00000010   Code   RO         1401    .text.ICM20608_Get_Fusion_Timer  icm20608.o
    0x00001f00   0x00001f00   0x00000034   Code   RO         1397    .text.ICM20608_Get_Gyro_Offset  icm20608.o
    0x00001f34   0x00001f34   0x00000170   Code   RO         1405    .text.ICM20608_Get_Temperature_Range  icm20608.o
    0x000020a4   0x000020a4   0x0000000c   Code   RO         1409    .text.ICM20608_Get_Yaw_Drift_Rate  icm20608.o
    0x000020b0   0x000020b0   0x0000000c   Code   RO         1407    .text.ICM20608_Get_Yaw_Quality  icm20608.o
    0x000020bc   0x000020bc   0x00000008   Code   RO         1403    .text.ICM20608_Is_Temperature_Stable  icm20608.o
    0x000020c4   0x000020c4   0x000003b8   Code   RO         1421    .text.ICM20608_Read_Data  icm20608.o
    0x0000247c   0x0000247c   0x000003f8   Code   RO         1423    .text.ICM20608_Update_Angles  icm20608.o
    0x00002874   0x00002874   0x00000010   Code   RO         1512    .text.IMU_App_Get_Angles  imu_app.o
    0x00002884   0x00002884   0x00000008   Code   RO         1516    .text.IMU_App_Get_Calib_Status  imu_app.o
    0x0000288c   0x0000288c   0x00000008   Code   RO         1546    .text.IMU_App_Get_Calibration_Status  imu_app.o
    0x00002894   0x00002894   0x00000038   Code   RO         1548    .text.IMU_App_Get_Calibration_Status_String  imu_app.o
    0x000028cc   0x000028cc   0x00000008   Code   RO         1520    .text.IMU_App_Get_Fusion_Status  imu_app.o
    0x000028d4   0x000028d4   0x00000008   Code   RO         1522    .text.IMU_App_Get_Fusion_Timer  imu_app.o
    0x000028dc   0x000028dc   0x00000008   Code   RO         1518    .text.IMU_App_Get_Gyro_Offset  imu_app.o
    0x000028e4   0x000028e4   0x0000003c   Code   RO         1554    .text.IMU_App_Get_Performance_Stats  imu_app.o
    0x00002920   0x00002920   0x00000020   Code   RO         1530    .text.IMU_App_Get_System_Status  imu_app.o
    0x00002940   0x00002940   0x00000008   Code   RO         1558    .text.IMU_App_Get_Temperature  imu_app.o
    0x00002948   0x00002948   0x00000008   Code   RO         1528    .text.IMU_App_Get_Temperature_Range  imu_app.o
    0x00002950   0x00002950   0x00000050   Code   RO         1552    .text.IMU_App_Get_Test_Results  imu_app.o
    0x000029a0   0x000029a0   0x0000006c   Code   RO         1556    .text.IMU_App_Get_Test_Status_String  imu_app.o
    0x00002a0c   0x00002a0c   0x00000008   Code   RO         1534    .text.IMU_App_Get_Yaw_Drift_Rate  imu_app.o
    0x00002a14   0x00002a14   0x00000008   Code   RO         1532    .text.IMU_App_Get_Yaw_Quality  imu_app.o
    0x00002a1c   0x00002a1c   0x00000054   Code   RO         1538    .text.IMU_App_Get_Yaw_Status_String  imu_app.o
    0x00002a70   0x00002a70   0x00000014   Code   RO         1560    .text.IMU_App_Is_Ready  imu_app.o
    0x00002a84   0x00002a84   0x00000012   Code   RO         1524    .text.IMU_App_Is_Stationary  imu_app.o
    0x00002a96   0x00002a96   0x00000008   Code   RO         1526    .text.IMU_App_Is_Temperature_Stable  imu_app.o
    0x00002a9e   0x00002a9e   0x00000002   PAD
    0x00002aa0   0x00002aa0   0x0000006c   Code   RO         1510    .text.IMU_App_Update  imu_app.o
    0x00002b0c   0x00002b0c   0x0000016c   Code   RO         1138    .text.LCD_CLS       oled.o
    0x00002c78   0x00002c78   0x00000060   Code   RO         1142    .text.LCD_P6x8Char  oled.o
    0x00002cd8   0x00002cd8   0x0000008c   Code   RO         1140    .text.LCD_P6x8Str   oled.o
    0x00002d64   0x00002d64   0x000000ec   Code   RO         1156    .text.LCD_clear_L   oled.o
    0x00002e50   0x00002e50   0x00000018   Code   RO          883    .text.LED_App_Init  led_app.o
    0x00002e68   0x00002e68   0x00000148   Code   RO         1574    .text.LPButterworth  filter.o
    0x00002fb0   0x00002fb0   0x00000034   Code   RO         1041    .text.Motor_App_Init  motor_app.o
    0x00002fe4   0x00002fe4   0x00000010   Code   RO         1106    .text.NMI_Handler   system.o
    0x00002ff4   0x00002ff4   0x0000002c   Code   RO         1160    .text.OLED_Init     oled.o
    0x00003020   0x00003020   0x00000084   Code   RO         1124    .text.OLED_WrCmd    oled.o
    0x000030a4   0x000030a4   0x0000008c   Code   RO         1122    .text.OLED_WrDat    oled.o
    0x00003130   0x00003130   0x0000003c   Code   RO         1460    .text.QEI0_IRQHandler  encoder_driver.o
    0x0000316c   0x0000316c   0x00000048   Code   RO         1462    .text.QEI1_IRQHandler  encoder_driver.o
    0x000031b4   0x000031b4   0x000000d4   Code   RO          937    .text.Read_Button_State_One  nbutton.o
    0x00003288   0x00003288   0x00000064   Code   RO          827    .text.SYSCFG_DL_ADC12_0_init  ti_msp_dl_config.o
    0x000032ec   0x000032ec   0x00000018   Code   RO          839    .text.SYSCFG_DL_DMA_CH0_init  ti_msp_dl_config.o
    0x00003304   0x00003304   0x00000008   Code   RO          829    .text.SYSCFG_DL_DMA_init  ti_msp_dl_config.o
    0x0000330c   0x0000330c   0x0000014c   Code   RO          797    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00003458   0x00003458   0x00000054   Code   RO          815    .text.SYSCFG_DL_I2C_0_init  ti_msp_dl_config.o
    0x000034ac   0x000034ac   0x000000c8   Code   RO          801    .text.SYSCFG_DL_PWM_0_init  ti_msp_dl_config.o
    0x00003574   0x00003574   0x0000007c   Code   RO          803    .text.SYSCFG_DL_PWM_1_init  ti_msp_dl_config.o
    0x000035f0   0x000035f0   0x0000007c   Code   RO          805    .text.SYSCFG_DL_PWM_2_init  ti_msp_dl_config.o
    0x0000366c   0x0000366c   0x0000004c   Code   RO          825    .text.SYSCFG_DL_SPI_0_init  ti_msp_dl_config.o
    0x000036b8   0x000036b8   0x00000030   Code   RO          833    .text.SYSCFG_DL_SYSCTL_CLK_init  ti_msp_dl_config.o
    0x000036e8   0x000036e8   0x0000009c   Code   RO          799    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00003784   0x00003784   0x00000030   Code   RO          831    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x000037b4   0x000037b4   0x00000048   Code   RO          807    .text.SYSCFG_DL_TIMER_G0_init  ti_msp_dl_config.o
    0x000037fc   0x000037fc   0x0000004c   Code   RO          813    .text.SYSCFG_DL_TIMER_G12_init  ti_msp_dl_config.o
    0x00003848   0x00003848   0x00000050   Code   RO          809    .text.SYSCFG_DL_TIMER_G6_init  ti_msp_dl_config.o
    0x00003898   0x00003898   0x0000004c   Code   RO          811    .text.SYSCFG_DL_TIMER_G8_init  ti_msp_dl_config.o
    0x000038e4   0x000038e4   0x0000007c   Code   RO          817    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00003960   0x00003960   0x00000078   Code   RO          819    .text.SYSCFG_DL_UART_1_init  ti_msp_dl_config.o
    0x000039d8   0x000039d8   0x000000a0   Code   RO          821    .text.SYSCFG_DL_UART_2_init  ti_msp_dl_config.o
    0x00003a78   0x00003a78   0x00000088   Code   RO          823    .text.SYSCFG_DL_UART_3_init  ti_msp_dl_config.o
    0x00003b00   0x00003b00   0x00000090   Code   RO          793    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00003b90   0x00003b90   0x000000d0   Code   RO          795    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00003c60   0x00003c60   0x0000000c   Code   RO         1080    .text.SysTick_Handler  system.o
    0x00003c6c   0x00003c6c   0x0000001c   Code   RO         1310    .text.TIMG0_IRQHandler  ntimer.o
    0x00003c88   0x00003c88   0x00000020   Code   RO         1316    .text.TIMG12_IRQHandler  ntimer.o
    0x00003ca8   0x00003ca8   0x00000028   Code   RO         1312    .text.TIMG6_IRQHandler  ntimer.o
    0x00003cd0   0x00003cd0   0x0000001c   Code   RO         1314    .text.TIMG8_IRQHandler  ntimer.o
    0x00003cec   0x00003cec   0x0000003c   Code   RO         1013    .text.UART0_App_GetRxData  uart_app.o
    0x00003d28   0x00003d28   0x00000024   Code   RO         1001    .text.UART0_App_Init  uart_app.o
    0x00003d4c   0x00003d4c   0x00000008   Code   RO         1017    .text.UART0_App_IsRxComplete  uart_app.o
    0x00003d54   0x00003d54   0x0000003c   Code   RO         1019    .text.UART0_App_RxCallback  uart_app.o
    0x00003d90   0x00003d90   0x0000002c   Code   RO         1011    .text.UART0_App_RxProcess  uart_app.o
    0x00003dbc   0x00003dbc   0x0000001c   Code   RO         1009    .text.UART0_App_SendBytes  uart_app.o
    0x00003dd8   0x00003dd8   0x0000003c   Code   RO         1344    .text.UART0_IRQHandler  nuart.o
    0x00003e14   0x00003e14   0x0000001c   Code   RO         1346    .text.UART1_IRQHandler  nuart.o
    0x00003e30   0x00003e30   0x00000030   Code   RO         1348    .text.UART2_IRQHandler  nuart.o
    0x00003e60   0x00003e60   0x0000001c   Code   RO         1350    .text.UART3_IRQHandler  nuart.o
    0x00003e7c   0x00003e7c   0x000000b4   Code   RO         1092    .text.delay_ms      system.o
    0x00003f30   0x00003f30   0x00000008   Code   RO         1162    .text.display_6_8_number  oled.o
    0x00003f38   0x00003f38   0x00000008   Code   RO         1166    .text.display_6_8_string  oled.o
    0x00003f40   0x00003f40   0x00000050   Code   RO          943    .text.get_key_short_press  nbutton.o
    0x00003f90   0x00003f90   0x000000b8   Code   RO         1464    .text.get_left_motor_speed  encoder_driver.o
    0x00004048   0x00004048   0x000000a0   Code   RO         1466    .text.get_right_motor_speed  encoder_driver.o
    0x000040e8   0x000040e8   0x00000050   Code   RO         1100    .text.get_systime   system.o
    0x00004138   0x00004138   0x0000000c   Code   RO         1468    .text.get_wheel_speed  encoder_driver.o
    0x00004144   0x00004144   0x00000008   Code   RO         1381    .text.i2creadnbyte  ni2c.o
    0x0000414c   0x0000414c   0x0000003a   Code   RO          784    .text.main          main.o
    0x00004186   0x00004186   0x00000002   PAD
    0x00004188   0x00004188   0x00000030   Code   RO         1086    .text.millis        system.o
    0x000041b8   0x000041b8   0x00000078   Code   RO         1082    .text.ncontroller_set_priority  system.o
    0x00004230   0x00004230   0x00000054   Code   RO          939    .text.read_button_state_all  nbutton.o
    0x00004284   0x00004284   0x00000ef8   Code   RO         1495    .text.screen_display  ui.o
    0x0000517c   0x0000517c   0x000000b8   Code   RO         1576    .text.set_cutoff_frequency  filter.o
    0x00005234   0x00005234   0x0000013c   Code   RO         1188    .text.ssd1306_begin  ssd1306.o
    0x00005370   0x00005370   0x0000006c   Code   RO         1306    .text.timer_irq_config  ntimer.o
    0x000053dc   0x000053dc   0x0000002a   Code   RO         1033    .text.uart_process  uart_process.o
    0x00005406   0x00005406   0x00000002   PAD
    0x00005408   0x00005408   0x0000005c   Code   RO         1342    .text.usart_irq_config  nuart.o
    0x00005464   0x00005464   0x000002e4   Code   RO         1144    .text.write_6_8_number  oled.o
    0x00005748   0x00005748   0x000001f6   Code   RO         1706    .text_divfast       c_p.l(aeabi_sdivfast.o)
    0x0000593e   0x0000593e   0x00000032   Code   RO         1804    i.__ARM_common_ll_muluu  m_ps.l(rredf.o)
    0x00005970   0x00005970   0x00000022   Code   RO         1785    i.__ARM_fpclassifyf  m_ps.l(fpclassifyf.o)
    0x00005992   0x00005992   0x0000000a   Code   RO         1788    i.__mathlib_flt_infnan  m_ps.l(funder.o)
    0x0000599c   0x0000599c   0x00000008   Code   RO         1789    i.__mathlib_flt_infnan2  m_ps.l(funder.o)
    0x000059a4   0x000059a4   0x0000000c   Code   RO         1790    i.__mathlib_flt_invalid  m_ps.l(funder.o)
    0x000059b0   0x000059b0   0x0000000e   Code   RO         1793    i.__mathlib_flt_underflow  m_ps.l(funder.o)
    0x000059be   0x000059be   0x00000002   PAD
    0x000059c0   0x000059c0   0x00000180   Code   RO         1801    i.__mathlib_rredf2  m_ps.l(rredf.o)
    0x00005b40   0x00005b40   0x00000016   Code   RO         1723    i._feq              fz_ps.l(fcmp.o)
    0x00005b56   0x00005b56   0x00000016   Code   RO         1724    i._fgeq             fz_ps.l(fcmp.o)
    0x00005b6c   0x00005b6c   0x00000016   Code   RO         1725    i._fgr              fz_ps.l(fcmp.o)
    0x00005b82   0x00005b82   0x0000001a   Code   RO         1726    i._fleq             fz_ps.l(fcmp.o)
    0x00005b9c   0x00005b9c   0x00000016   Code   RO         1727    i._fls              fz_ps.l(fcmp.o)
    0x00005bb2   0x00005bb2   0x00000002   PAD
    0x00005bb4   0x00005bb4   0x0000029c   Code   RO         1747    i.atan2f            m_ps.l(atan2f.o)
    0x00005e50   0x00005e50   0x0000002c   Code   RO         1751    i.sqrtf             m_ps.l(sqrtf.o)
    0x00005e7c   0x00005e7c   0x00000170   Code   RO         1755    i.tanf              m_ps.l(tanf.o)
    0x00005fec   0x00005fec   0x0000008c   Code   RO         1717    x$fpl$fadd          fz_ps.l(faddsub.o)
    0x00006078   0x00006078   0x00000054   Code   RO         1774    x$fpl$feqf          fz_ps.l(feqf.o)
    0x000060cc   0x000060cc   0x00000054   Code   RO         1776    x$fpl$fgeqf         fz_ps.l(fgef.o)
    0x00006120   0x00006120   0x00000054   Code   RO         1778    x$fpl$fleqf         fz_ps.l(flef.o)
    0x00006174   0x00006174   0x000000b0   Code   RO         1744    x$fpl$fmul          fz_ps.l(fmul.o)
    0x00006224   0x00006224   0x00000018   Code   RO         1718    x$fpl$frsb          fz_ps.l(faddsub.o)
    0x0000623c   0x0000623c   0x000000d0   Code   RO         1719    x$fpl$fsub          fz_ps.l(faddsub.o)
    0x0000630c   0x0000630c   0x00000000   Code   RO         1784    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x0000630c   0x0000630c   0x00000040   Data   RO         1736    .constdata          fz_ps.l(fdiv.o)
    0x0000634c   0x0000634c   0x00000020   Data   RO         1802    .constdata          m_ps.l(rredf.o)
    0x0000636c   0x0000636c   0x00000228   Data   RO         1168    .rodata.F6x8        oled.o
    0x00006594   0x00006594   0x00000400   Data   RO         1170    .rodata.NC_Logo     oled.o
    0x00006994   0x00006994   0x00000040   Data   RO         1633    .rodata.crc32_table  calibration.o
    0x000069d4   0x000069d4   0x00000008   Data   RO          873    .rodata.gADC12_0ClockConfig  ti_msp_dl_config.o
    0x000069dc   0x000069dc   0x00000018   Data   RO          874    .rodata.gDMA_CH0Config  ti_msp_dl_config.o
    0x000069f4   0x000069f4   0x00000002   Data   RO          862    .rodata.gI2C_0ClockConfig  ti_msp_dl_config.o
    0x000069f6   0x000069f6   0x00000003   Data   RO          848    .rodata.gPWM_0ClockConfig  ti_msp_dl_config.o
    0x000069f9   0x000069f9   0x00000003   PAD
    0x000069fc   0x000069fc   0x00000008   Data   RO          849    .rodata.gPWM_0Config  ti_msp_dl_config.o
    0x00006a04   0x00006a04   0x00000003   Data   RO          850    .rodata.gPWM_1ClockConfig  ti_msp_dl_config.o
    0x00006a07   0x00006a07   0x00000001   PAD
    0x00006a08   0x00006a08   0x00000008   Data   RO          851    .rodata.gPWM_1Config  ti_msp_dl_config.o
    0x00006a10   0x00006a10   0x00000003   Data   RO          852    .rodata.gPWM_2ClockConfig  ti_msp_dl_config.o
    0x00006a13   0x00006a13   0x00000001   PAD
    0x00006a14   0x00006a14   0x00000008   Data   RO          853    .rodata.gPWM_2Config  ti_msp_dl_config.o
    0x00006a1c   0x00006a1c   0x00000002   Data   RO          871    .rodata.gSPI_0_clockConfig  ti_msp_dl_config.o
    0x00006a1e   0x00006a1e   0x0000000a   Data   RO          872    .rodata.gSPI_0_config  ti_msp_dl_config.o
    0x00006a28   0x00006a28   0x00000028   Data   RO          847    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00006a50   0x00006a50   0x00000003   Data   RO          854    .rodata.gTIMER_G0ClockConfig  ti_msp_dl_config.o
    0x00006a53   0x00006a53   0x00000001   PAD
    0x00006a54   0x00006a54   0x00000014   Data   RO          855    .rodata.gTIMER_G0TimerConfig  ti_msp_dl_config.o
    0x00006a68   0x00006a68   0x00000003   Data   RO          860    .rodata.gTIMER_G12ClockConfig  ti_msp_dl_config.o
    0x00006a6b   0x00006a6b   0x00000001   PAD
    0x00006a6c   0x00006a6c   0x00000014   Data   RO          861    .rodata.gTIMER_G12TimerConfig  ti_msp_dl_config.o
    0x00006a80   0x00006a80   0x00000003   Data   RO          856    .rodata.gTIMER_G6ClockConfig  ti_msp_dl_config.o
    0x00006a83   0x00006a83   0x00000001   PAD
    0x00006a84   0x00006a84   0x00000014   Data   RO          857    .rodata.gTIMER_G6TimerConfig  ti_msp_dl_config.o
    0x00006a98   0x00006a98   0x00000003   Data   RO          858    .rodata.gTIMER_G8ClockConfig  ti_msp_dl_config.o
    0x00006a9b   0x00006a9b   0x00000001   PAD
    0x00006a9c   0x00006a9c   0x00000014   Data   RO          859    .rodata.gTIMER_G8TimerConfig  ti_msp_dl_config.o
    0x00006ab0   0x00006ab0   0x00000002   Data   RO          863    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x00006ab2   0x00006ab2   0x0000000a   Data   RO          864    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x00006abc   0x00006abc   0x00000002   Data   RO          865    .rodata.gUART_1ClockConfig  ti_msp_dl_config.o
    0x00006abe   0x00006abe   0x0000000a   Data   RO          866    .rodata.gUART_1Config  ti_msp_dl_config.o
    0x00006ac8   0x00006ac8   0x00000002   Data   RO          867    .rodata.gUART_2ClockConfig  ti_msp_dl_config.o
    0x00006aca   0x00006aca   0x0000000a   Data   RO          868    .rodata.gUART_2Config  ti_msp_dl_config.o
    0x00006ad4   0x00006ad4   0x00000002   Data   RO          869    .rodata.gUART_3ClockConfig  ti_msp_dl_config.o
    0x00006ad6   0x00006ad6   0x0000000a   Data   RO          870    .rodata.gUART_3Config  ti_msp_dl_config.o
    0x00006ae0   0x00006ae0   0x0000040b   Data   RO         1499    .rodata.str1.1      ui.o
    0x00006eeb   0x00006eeb   0x00000020   Data   RO         1565    .rodata.str1.1      imu_app.o
    0x00006f0b   0x00006f0b   0x00000005   PAD
    0x00006f10   0x00006f10   0x00000020   Data   RO         1992    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00006f30, Size: 0x00003910, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00006f30   0x0000003c   Data   RW         1470    .data.NEncoder      encoder_driver.o
    0x2020003c   0x00006f6c   0x00000001   Data   RW         1632    .data.auto_save_enabled  calibration.o
    0x2020003d   0x00006f6d   0x00000003   PAD
    0x20200040   0x00006f70   0x00000010   Data   RW         1471    .data.trackless_motor  encoder_driver.o
    0x20200050   0x00006f80   0x00000004   Data   RW         1440    .data.yaw_quality_score  icm20608.o
    0x20200054   0x00006f84   0x00000004   PAD
    0x20200058        -       0x00000060   Zero   RW         1835    .bss                c_p.l(libspace.o)
    0x202000b8        -       0x00000001   Zero   RW         1284    .bss.HEIGHT         ssd1306.o
    0x202000b9   0x00006f84   0x00000003   PAD
    0x202000bc        -       0x00000004   Zero   RW         1447    .bss.ICM20608_Update_Angles.prev_gyro_z  icm20608.o
    0x202000c0        -       0x00000002   Zero   RW         1564    .bss.IMU_App_Update.retry_count  imu_app.o
    0x202000c2        -       0x00000001   Zero   RW         1283    .bss.WIDTH          ssd1306.o
    0x202000c3   0x00006f84   0x00000001   PAD
    0x202000c4        -       0x00000120   Zero   RW          949    .bss._button        nbutton.o
    0x202001e4        -       0x00000001   Zero   RW         1292    .bss._cp437         ssd1306.o
    0x202001e5   0x00006f84   0x00000001   PAD
    0x202001e6        -       0x00000002   Zero   RW         1281    .bss._height        ssd1306.o
    0x202001e8        -       0x00000001   Zero   RW         1285    .bss._vccstate      ssd1306.o
    0x202001e9   0x00006f84   0x00000001   PAD
    0x202001ea        -       0x00000002   Zero   RW         1280    .bss._width         ssd1306.o
    0x202001ec        -       0x00000048   Zero   RW         1429    .bss.accel_lpf_buf  icm20608.o
    0x20200234        -       0x00000048   Zero   RW         1431    .bss.accel_lpf_param  icm20608.o
    0x2020027c        -       0x00000001   Zero   RW         1563    .bss.auto_calib_requested  imu_app.o
    0x2020027d   0x00006f84   0x00000003   PAD
    0x20200280        -       0x0000001c   Zero   RW          992    .bss.buzzer         buzzer_app.o
    0x2020029c        -       0x00000064   Zero   RW         1628    .bss.calib_data     calibration.o
    0x20200300        -       0x00000001   Zero   RW         1629    .bss.calib_status   calibration.o
    0x20200301   0x00006f84   0x00000001   PAD
    0x20200302        -       0x00000002   Zero   RW         1286    .bss.cursor_x       ssd1306.o
    0x20200304        -       0x00000002   Zero   RW         1287    .bss.cursor_y       ssd1306.o
    0x20200306        -       0x00000001   Zero   RW         1631    .bss.data_changed   calibration.o
    0x20200307   0x00006f84   0x00000001   PAD
    0x20200308        -       0x00000004   Zero   RW         1498    .bss.display_counter  ui.o
    0x2020030c        -       0x00000001   Zero   RW         1473    .bss.filter_initialized  encoder_driver.o
    0x2020030d   0x00006f84   0x00000003   PAD
    0x20200310        -       0x00000018   Zero   RW         1432    .bss.fusion_offset  icm20608.o
    0x20200328        -       0x00000001   Zero   RW         1433    .bss.fusion_offset_init  icm20608.o
    0x20200329   0x00006f84   0x00000003   PAD
    0x2020032c        -       0x000000bc   Zero   RW          841    .bss.gPWM_0Backup   ti_msp_dl_config.o
    0x202003e8        -       0x000000bc   Zero   RW          842    .bss.gPWM_1Backup   ti_msp_dl_config.o
    0x202004a4        -       0x00000078   Zero   RW          843    .bss.gPWM_2Backup   ti_msp_dl_config.o
    0x2020051c        -       0x00000028   Zero   RW          846    .bss.gSPI_0Backup   ti_msp_dl_config.o
    0x20200544        -       0x00000078   Zero   RW          844    .bss.gTIMER_G6Backup  ti_msp_dl_config.o
    0x202005bc        -       0x00000030   Zero   RW          845    .bss.gUART_3Backup  ti_msp_dl_config.o
    0x202005ec        -       0x00000002   Zero   RW         1472    .bss.get_left_motor_speed.cnt1  encoder_driver.o
    0x202005ee        -       0x00000002   Zero   RW         1476    .bss.get_right_motor_speed.cnt2  encoder_driver.o
    0x202005f0        -       0x00000001   Zero   RW         1435    .bss.gyro_calib_done  icm20608.o
    0x202005f1        -       0x00000001   Zero   RW         1436    .bss.gyro_calib_in_progress  icm20608.o
    0x202005f2   0x00006f84   0x00000002   PAD
    0x202005f4        -       0x00000048   Zero   RW         1428    .bss.gyro_lpf_buf   icm20608.o
    0x2020063c        -       0x00000048   Zero   RW         1430    .bss.gyro_lpf_param  icm20608.o
    0x20200684        -       0x0000000c   Zero   RW         1434    .bss.gyro_offset    icm20608.o
    0x20200690        -       0x00000004   Zero   RW         1113    .bss.hf_cnt         system.o
    0x20200694        -       0x0000000c   Zero   RW         1071    .bss.hui_pin_states  hui_app.o
    0x202006a0        -       0x00000044   Zero   RW         1427    .bss.imu_data       icm20608.o
    0x202006e4        -       0x00000018   Zero   RW         1562    .bss.imu_status     imu_app.o
    0x202006fc        -       0x00000040   Zero   RW         1111    .bss.irq_poriority  system.o
    0x2020073c        -       0x00000018   Zero   RW         1475    .bss.left_speed_filter_buf  encoder_driver.o
    0x20200754        -       0x00000004   Zero   RW         1112    .bss.nmi_cnt        system.o
    0x20200758        -       0x00000002   Zero   RW         1497    .bss.page_number    ui.o
    0x2020075a   0x00006f84   0x00000002   PAD
    0x2020075c        -       0x00000018   Zero   RW         1477    .bss.right_speed_filter_buf  encoder_driver.o
    0x20200774        -       0x00000001   Zero   RW         1282    .bss.rotation       ssd1306.o
    0x20200775   0x00006f84   0x00000003   PAD
    0x20200778        -       0x00000004   Zero   RW         1630    .bss.save_timer     calibration.o
    0x2020077c        -       0x00000018   Zero   RW         1474    .bss.speed_filter_param  encoder_driver.o
    0x20200794        -       0x00000004   Zero   RW         1110    .bss.sysTickUptime  system.o
    0x20200798        -       0x00000010   Zero   RW         1326    .bss.t_g0           ntimer.o
    0x202007a8        -       0x00000010   Zero   RW         1327    .bss.t_g1           ntimer.o
    0x202007b8        -       0x00000010   Zero   RW         1329    .bss.t_g12          ntimer.o
    0x202007c8        -       0x00000010   Zero   RW         1328    .bss.t_g8           ntimer.o
    0x202007d8        -       0x00000028   Zero   RW         1439    .bss.temp_history   icm20608.o
    0x20200800        -       0x00000001   Zero   RW         1438    .bss.temp_history_full  icm20608.o
    0x20200801        -       0x00000001   Zero   RW         1448    .bss.temp_history_index  icm20608.o
    0x20200802   0x00006f84   0x00000002   PAD
    0x20200804        -       0x00000004   Zero   RW         1449    .bss.temp_stable_timer  icm20608.o
    0x20200808        -       0x00000001   Zero   RW         1437    .bss.temperature_stable_flag  icm20608.o
    0x20200809   0x00006f84   0x00000001   PAD
    0x2020080a        -       0x00000002   Zero   RW         1289    .bss.textbgcolor    ssd1306.o
    0x2020080c        -       0x00000002   Zero   RW         1290    .bss.textcolor      ssd1306.o
    0x2020080e        -       0x00000001   Zero   RW         1288    .bss.textsize       ssd1306.o
    0x2020080f   0x00006f84   0x00000001   PAD
    0x20200810        -       0x00000080   Zero   RW         1024    .bss.uart0_rx_buffer  uart_app.o
    0x20200890        -       0x00000001   Zero   RW         1023    .bss.uart0_rx_complete  uart_app.o
    0x20200891   0x00006f84   0x00000001   PAD
    0x20200892        -       0x00000002   Zero   RW         1021    .bss.uart0_rx_index  uart_app.o
    0x20200894        -       0x00000004   Zero   RW         1022    .bss.uart0_rx_tick  uart_app.o
    0x20200898        -       0x00000010   Zero   RW         1364    .bss.uart2_dma_t    nuart.o
    0x202008a8        -       0x00000001   Zero   RW         1291    .bss.wrap           ssd1306.o
    0x202008a9   0x00006f84   0x00000003   PAD
    0x202008ac        -       0x00000004   Zero   RW         1441    .bss.yaw_drift_rate  icm20608.o
    0x202008b0        -       0x00000050   Zero   RW         1445    .bss.yaw_history    icm20608.o
    0x20200900        -       0x00000001   Zero   RW         1443    .bss.yaw_history_full  icm20608.o
    0x20200901        -       0x00000001   Zero   RW         1446    .bss.yaw_history_index  icm20608.o
    0x20200902   0x00006f84   0x00000002   PAD
    0x20200904        -       0x00000004   Zero   RW         1444    .bss.yaw_lpf_output  icm20608.o
    0x20200908        -       0x00000001   Zero   RW         1442    .bss.yaw_reset_request  icm20608.o
    0x20200909   0x00006f84   0x00000007   PAD
    0x20200910        -       0x00001000   Zero   RW          775    HEAP                startup_mspm0g350x_uvision.o
    0x20201910        -       0x00002000   Zero   RW          774    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       212         16          0          0         28       7303   buzzer_app.o
      1156         36         64          1        106       9869   calibration.o
        64          8          0          0          0       4529   dl_adc12.o
        10          0          0          0          0        613   dl_common.o
        68          4          0          0          0       4443   dl_dma.o
       234          4          0          0          0       9497   dl_i2c.o
        86          8          0          0          0      15721   dl_spi.o
       420         28          0          0          0      18939   dl_sysctl_mspm0g1x0x_g3x0x.o
       632        192          0          0          0      36317   dl_timer.o
       150         12          0          0          0      14532   dl_uart.o
       508         80          0         76         77       7134   encoder_driver.o
       512         20          0          0          0       2225   filter.o
       200          8          0          0          0       2021   fusionoffset.o
       132         12          0          0         12       6293   hui_app.o
      2836        232          0          4        537       9720   icm20608.o
       662        108         32          0         27       7224   imu_app.o
        76          8          0          0          0       4493   interrupt_handler.o
        24          4          0          0          0      11127   led_app.o
        58          0          0          0          0        542   main.o
        52          4          0          0          0       5984   motor_app.o
       488         20          0          0        288       6899   nbutton.o
       692         20          0          0          0      11907   ni2c.o
       236         64          0          0         64       7487   ntimer.o
       256         64          0          0         16       8535   nuart.o
      2112         96       1576          0          0      36538   oled.o
       316         52          0          0         19      33251   ssd1306.o
        48         22        192          0      12288        792   startup_mspm0g350x_uvision.o
       660         52          0          0         76       7797   system.o
      2520        468        259          0        704      56807   ti_msp_dl_config.o
       236         24          0          0        135       5266   uart_app.o
        42          0          0          0          0        716   uart_process.o
      3832        736       1035          0          6       7173   ui.o

    ----------------------------------------------------------------------
     19544       <USER>       <GROUP>         84      14428     361694   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0         14          3         45          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
       108         18          0          0          0         76   _printf_dec.o
       176          0          0          0          0         84   _printf_intcommon.o
         2          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0          0   _printf_u.o
        22          0          0          0          0         92   _rserrno.o
      1006          4          0          0          0        184   aeabi_sdivfast.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
        64          0          0          0          0        108   rt_memclr.o
       186          0          0          0          0        144   rt_memcpy.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        40          0          0          0          0         60   rtudiv10.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        46          0          0          0          0         60   cmpret.o
       372          8          0          0          0        240   faddsub.o
       114          0          0          0          0        340   fcmp.o
       100          4          0          0          0         68   fcmpin.o
       352         10         64          0          0         92   fdiv.o
        84          4          0          0          0         76   feqf.o
        76          0          0          0          0         68   ffixi.o
        48          0          0          0          0         60   ffixui.o
        94          0          0          0          0         92   fflti.o
        84          4          0          0          0         76   fgef.o
        84          4          0          0          0         76   flef.o
       176          4          0          0          0         80   fmul.o
        16          6          0          0          0         68   fnan2.o
       128          6          0          0          0         68   frnd.o
        68          4          0          0          0         68   fscalbn.o
       144          4          0          0          0         72   fsqrt.o
        94          0          0          0          0         68   retnan.o
         0          0          0          0          0          0   usenofp.o
       668         76          0          0          0        120   atan2f.o
        34          0          0          0          0         60   fpclassifyf.o
        44          0          0          0          0        272   funder.o
       434         18         32          0          0        168   rredf.o
        44          0          0          0          0         72   sqrtf.o
       368         56          0          0          0        100   tanf.o

    ----------------------------------------------------------------------
      5620        <USER>         <GROUP>          0         96       3828   Library Totals
        30          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1918         44          0          0         96       1364   c_p.l
      2080         58         64          0          0       1672   fz_ps.l
      1592        150         32          0          0        792   m_ps.l

    ----------------------------------------------------------------------
      5620        <USER>         <GROUP>          0         96       3828   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     25164       2658       3300         84      14524     362918   Grand Totals
     25164       2658       3300         84      14524     362918   ELF Image Totals
     25164       2658       3300         84          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                28464 (  27.80kB)
    Total RW  Size (RW Data + ZI Data)             14608 (  14.27kB)
    Total ROM Size (Code + RO Data + RW Data)      28548 (  27.88kB)

==============================================================================

