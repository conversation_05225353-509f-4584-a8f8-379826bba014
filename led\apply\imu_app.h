#ifndef __IMU_APP_H
#define __IMU_APP_H

#include "icm20608.h"

// IMU应用层状态
typedef struct
{
    uint8_t init_success;     // 初始化成功标志
    uint8_t data_ready;       // 数据就绪标志
    uint32_t update_count;    // 更新计数器
    euler_angles current_angles; // 当前姿态角
    float temperature;        // 当前温度
}imu_app_status;

// 函数声明
void IMU_App_Init(void);
void IMU_App_Update(void);
void IMU_App_Get_Angles(euler_angles *angles);
float IMU_App_Get_Temperature(void);
uint8_t IMU_App_Is_Ready(void);

// 校准相关函数
void IMU_App_Start_Calibration(void);        // 启动陀螺仪校准
uint8_t IMU_App_Get_Calib_Status(void);      // 获取校准状态
void IMU_App_Get_Gyro_Offset(vector3f *offset); // 获取陀螺仪零偏值

// FusionOffset算法相关函数
uint8_t IMU_App_Get_Fusion_Status(void);     // 获取FusionOffset状态
uint32_t IMU_App_Get_Fusion_Timer(void);     // 获取FusionOffset计时器值
uint8_t IMU_App_Is_Stationary(void);         // 判断设备是否静止

// 温度稳定性检测相关函数
uint8_t IMU_App_Is_Temperature_Stable(void); // 获取温度稳定状态
float IMU_App_Get_Temperature_Range(void);   // 获取温度变化范围
uint8_t IMU_App_Get_System_Status(void);     // 获取IMU系统综合状态

// yaw值优化相关函数
uint32_t IMU_App_Get_Yaw_Quality(void);      // 获取yaw值质量评分
float IMU_App_Get_Yaw_Drift_Rate(void);      // 获取yaw值漂移率
void IMU_App_Reset_Yaw(void);                // 重置yaw值
const char* IMU_App_Get_Yaw_Status_String(void); // 获取yaw值状态字符串

// 校准数据持久化相关函数
uint8_t IMU_App_Save_Calibration(void);      // 保存校准数据
uint8_t IMU_App_Load_Calibration(void);      // 加载校准数据
uint8_t IMU_App_Reset_Calibration(void);     // 重置校准数据
calib_status_t IMU_App_Get_Calibration_Status(void); // 获取校准数据状态
const char* IMU_App_Get_Calibration_Status_String(void); // 获取校准状态字符串

// 测试和诊断相关函数
void IMU_App_Start_Drift_Test(void);         // 启动yaw值漂移测试
uint8_t IMU_App_Get_Test_Results(void);      // 获取测试结果
void IMU_App_Get_Performance_Stats(float *precision, float *drift_rate,
                                  uint32_t *quality, uint8_t *stability); // 获取性能统计
const char* IMU_App_Get_Test_Status_String(void); // 获取测试状态字符串

// 全局变量声明
extern imu_app_status imu_status;

#endif
