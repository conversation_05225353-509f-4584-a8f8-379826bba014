#include "ti_msp_dl_config.h"
#include "imu_app.h"
#include "icm20608.h"
#include "system.h"

// 全局变量定义
imu_app_status imu_status = {0};
static uint8_t auto_calib_requested = 0;  // 自动校准请求标志

/***************************************
函数名: IMU_App_Init
说明: IMU应用层初始化
参数: 无
返回: 无
***************************************/
void IMU_App_Init(void)
{
    // 初始化状态结构体
    imu_status.init_success = 0;
    imu_status.data_ready = 0;
    imu_status.update_count = 0;
    imu_status.current_angles.roll = 0.0f;
    imu_status.current_angles.pitch = 0.0f;
    imu_status.current_angles.yaw = 0.0f;
    imu_status.temperature = 25.0f;
    
    // 初始化ICM20608
    if(ICM20608_Init() == 0)
    {
        imu_status.init_success = 1;
        delay_ms(100); // 等待传感器稳定

        // 启动自动校准（延迟3秒让用户放置设备）
        delay_ms(3000);
        auto_calib_requested = 1;
    }
}

/***************************************
函数名: IMU_App_Update
说明: IMU数据更新(在定时器中调用)
参数: 无
返回: 无
***************************************/
void IMU_App_Update(void)
{
    if(!imu_status.init_success) return;

    // 处理自动校准请求
    if(auto_calib_requested)
    {
        if(ICM20608_Calibrate_Gyro() == 0)
        {
            // 校准成功
            auto_calib_requested = 0;
        }
        else
        {
            // 校准失败，稍后重试
            static uint16_t retry_count = 0;
            retry_count++;
            if(retry_count > 10) // 重试10次后放弃
            {
                auto_calib_requested = 0;
                retry_count = 0;
            }
        }
    }

    vector3f gyro, accel;
    float temp;

    // 读取传感器数据
    ICM20608_Read_Data(&gyro, &accel, &temp);

    // 更新姿态角
    ICM20608_Update_Angles();

    // 获取最新姿态角
    ICM20608_Get_Angles(&imu_status.current_angles);

    // 更新温度
    imu_status.temperature = temp;

    // 设置数据就绪标志
    imu_status.data_ready = 1;
    imu_status.update_count++;
}

/***************************************
函数名: IMU_App_Get_Angles
说明: 获取当前姿态角
参数: euler_angles *angles - 姿态角结构体指针
返回: 无
***************************************/
void IMU_App_Get_Angles(euler_angles *angles)
{
    *angles = imu_status.current_angles;
}

/***************************************
函数名: IMU_App_Start_Calibration
说明: 启动陀螺仪校准
参数: 无
返回: 无
***************************************/
void IMU_App_Start_Calibration(void)
{
    auto_calib_requested = 1;
}

/***************************************
函数名: IMU_App_Get_Calib_Status
说明: 获取校准状态
参数: 无
返回: uint8_t 校准状态(0-未校准, 1-校准中, 2-校准完成)
***************************************/
uint8_t IMU_App_Get_Calib_Status(void)
{
    return ICM20608_Get_Calib_Status();
}

/***************************************
函数名: IMU_App_Get_Gyro_Offset
说明: 获取陀螺仪零偏值
参数: vector3f *offset - 零偏值指针
返回: 无
***************************************/
void IMU_App_Get_Gyro_Offset(vector3f *offset)
{
    ICM20608_Get_Gyro_Offset(offset);
}

/***************************************
函数名: IMU_App_Get_Fusion_Status
说明: 获取FusionOffset算法状态
参数: 无
返回: uint8_t FusionOffset状态(0-未初始化, 1-已初始化)
***************************************/
uint8_t IMU_App_Get_Fusion_Status(void)
{
    return ICM20608_Get_Fusion_Status();
}

/***************************************
函数名: IMU_App_Get_Fusion_Timer
说明: 获取FusionOffset算法计时器值
参数: 无
返回: uint32_t 计时器值(用于判断静止状态持续时间)
***************************************/
uint32_t IMU_App_Get_Fusion_Timer(void)
{
    return ICM20608_Get_Fusion_Timer();
}

/***************************************
函数名: IMU_App_Is_Stationary
说明: 判断设备是否处于静止状态
参数: 无
返回: uint8_t 静止状态(0-运动中, 1-静止)
***************************************/
uint8_t IMU_App_Is_Stationary(void)
{
    uint32_t timer = IMU_App_Get_Fusion_Timer();
    // 如果计时器值大于200(1秒)，认为设备静止
    return (timer > 200) ? 1 : 0;
}

/***************************************
函数名: IMU_App_Is_Temperature_Stable
说明: 获取温度稳定状态
参数: 无
返回: uint8_t 温度稳定状态(0-不稳定, 1-稳定)
***************************************/
uint8_t IMU_App_Is_Temperature_Stable(void)
{
    return ICM20608_Is_Temperature_Stable();
}

/***************************************
函数名: IMU_App_Get_Temperature_Range
说明: 获取温度变化范围
参数: 无
返回: float 温度变化范围(°C)
***************************************/
float IMU_App_Get_Temperature_Range(void)
{
    return ICM20608_Get_Temperature_Range();
}

/***************************************
函数名: IMU_App_Get_System_Status
说明: 获取IMU系统综合状态
参数: 无
返回: uint8_t 系统状态(0-初始化中, 1-温度不稳定, 2-正常工作)
***************************************/
uint8_t IMU_App_Get_System_Status(void)
{
    if(!imu_status.init_success) return 0; // 初始化中
    if(!IMU_App_Is_Temperature_Stable()) return 1; // 温度不稳定
    return 2; // 正常工作
}

/***************************************
函数名: IMU_App_Get_Yaw_Quality
说明: 获取yaw值质量评分
参数: 无
返回: uint32_t yaw值质量评分(0-100)
***************************************/
uint32_t IMU_App_Get_Yaw_Quality(void)
{
    return ICM20608_Get_Yaw_Quality();
}

/***************************************
函数名: IMU_App_Get_Yaw_Drift_Rate
说明: 获取yaw值漂移率
参数: 无
返回: float yaw值漂移率(°/s)
***************************************/
float IMU_App_Get_Yaw_Drift_Rate(void)
{
    return ICM20608_Get_Yaw_Drift_Rate();
}

/***************************************
函数名: IMU_App_Reset_Yaw
说明: 重置yaw值
参数: 无
返回: 无
***************************************/
void IMU_App_Reset_Yaw(void)
{
    ICM20608_Reset_Yaw();
}

/***************************************
函数名: IMU_App_Get_Yaw_Status_String
说明: 获取yaw值状态字符串
参数: 无
返回: const char* 状态字符串
***************************************/
const char* IMU_App_Get_Yaw_Status_String(void)
{
    uint32_t quality = IMU_App_Get_Yaw_Quality();

    if(quality >= 90) return "Excellent";
    else if(quality >= 75) return "Good";
    else if(quality >= 60) return "Fair";
    else if(quality >= 40) return "Poor";
    else return "Bad";
}

/***************************************
函数名: uint8_t IMU_App_Save_Calibration
说明: 保存校准数据
参数: 无
返回: uint8_t 保存结果(0-成功, 1-失败)
***************************************/
uint8_t IMU_App_Save_Calibration(void)
{
    return ICM20608_Save_Calibration();
}

/***************************************
函数名: uint8_t IMU_App_Load_Calibration
说明: 加载校准数据
参数: 无
返回: uint8_t 加载结果(0-成功, 1-失败)
***************************************/
uint8_t IMU_App_Load_Calibration(void)
{
    return ICM20608_Load_Calibration();
}

/***************************************
函数名: uint8_t IMU_App_Reset_Calibration
说明: 重置校准数据
参数: 无
返回: uint8_t 重置结果(0-成功, 1-失败)
***************************************/
uint8_t IMU_App_Reset_Calibration(void)
{
    return ICM20608_Reset_Calibration();
}

/***************************************
函数名: calib_status_t IMU_App_Get_Calibration_Status
说明: 获取校准数据状态
参数: 无
返回: calib_status_t 校准状态
***************************************/
calib_status_t IMU_App_Get_Calibration_Status(void)
{
    return ICM20608_Get_Calibration_Status();
}

/***************************************
函数名: const char* IMU_App_Get_Calibration_Status_String
说明: 获取校准状态字符串
参数: 无
返回: const char* 状态字符串
***************************************/
const char* IMU_App_Get_Calibration_Status_String(void)
{
    calib_status_t status = IMU_App_Get_Calibration_Status();

    switch(status)
    {
        case CALIB_STATUS_NONE: return "None";
        case CALIB_STATUS_LOADING: return "Loading";
        case CALIB_STATUS_VALID: return "Valid";
        case CALIB_STATUS_INVALID: return "Invalid";
        case CALIB_STATUS_SAVING: return "Saving";
        case CALIB_STATUS_ERROR: return "Error";
        default: return "Unknown";
    }
}

/***************************************
函数名: void IMU_App_Start_Drift_Test
说明: 启动yaw值漂移测试
参数: 无
返回: 无
***************************************/
void IMU_App_Start_Drift_Test(void)
{
    // 重置yaw值和质量评估
    IMU_App_Reset_Yaw();

    // 等待系统稳定
    delay_ms(1000);
}

/***************************************
函数名: uint8_t IMU_App_Get_Test_Results
说明: 获取测试结果
参数: 无
返回: uint8_t 测试结果(0-未完成, 1-通过, 2-失败)
***************************************/
uint8_t IMU_App_Get_Test_Results(void)
{
    if(!imu_status.init_success) return 0; // 未初始化

    uint32_t quality = IMU_App_Get_Yaw_Quality();
    float drift_rate = fabsf(IMU_App_Get_Yaw_Drift_Rate());
    uint8_t temp_stable = IMU_App_Is_Temperature_Stable();
    uint8_t fusion_status = IMU_App_Get_Fusion_Status();

    // 检查各项指标
    uint8_t quality_pass = (quality >= 75) ? 1 : 0;           // 质量评分≥75
    uint8_t drift_pass = (drift_rate <= 0.1f) ? 1 : 0;       // 漂移率≤0.1°/s
    uint8_t temp_pass = temp_stable;                          // 温度稳定
    uint8_t fusion_pass = fusion_status;                      // FusionOffset工作

    // 综合评估
    uint8_t total_score = quality_pass + drift_pass + temp_pass + fusion_pass;

    if(total_score >= 3) return 1; // 通过（4项中至少3项通过）
    else if(total_score >= 1) return 0; // 测试中
    else return 2; // 失败
}

/***************************************
函数名: void IMU_App_Get_Performance_Stats
说明: 获取性能统计信息
参数: float *precision - 精度(°)
      float *drift_rate - 漂移率(°/s)
      uint32_t *quality - 质量评分
      uint8_t *stability - 稳定性状态
返回: 无
***************************************/
void IMU_App_Get_Performance_Stats(float *precision, float *drift_rate,
                                  uint32_t *quality, uint8_t *stability)
{
    if(precision) *precision = 0.5f; // 估计精度±0.5°
    if(drift_rate) *drift_rate = fabsf(IMU_App_Get_Yaw_Drift_Rate());
    if(quality) *quality = IMU_App_Get_Yaw_Quality();
    if(stability) *stability = IMU_App_Is_Temperature_Stable();
}

/***************************************
函数名: const char* IMU_App_Get_Test_Status_String
说明: 获取测试状态字符串
参数: 无
返回: const char* 测试状态字符串
***************************************/
const char* IMU_App_Get_Test_Status_String(void)
{
    uint8_t result = IMU_App_Get_Test_Results();

    switch(result)
    {
        case 0: return "Testing";
        case 1: return "PASS";
        case 2: return "FAIL";
        default: return "Unknown";
    }
}

/***************************************
函数名: IMU_App_Get_Temperature
说明: 获取当前温度
参数: 无
返回: float 温度值
***************************************/
float IMU_App_Get_Temperature(void)
{
    return imu_status.temperature;
}

/***************************************
函数名: IMU_App_Is_Ready
说明: 检查IMU是否就绪
参数: 无
返回: uint8_t 就绪状态(1-就绪, 0-未就绪)
***************************************/
uint8_t IMU_App_Is_Ready(void)
{
    return (imu_status.init_success && imu_status.data_ready);
}
