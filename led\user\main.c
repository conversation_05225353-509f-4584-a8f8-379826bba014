/****************************************************************************************
	无名创新地面站通讯	
		PA10-->UART0-TXD	USB转TTL-RXD
		PA11-->UART0-RXD	USB转TTL-TXD
	机器视觉OPENMV4 MINI	
		PA8-UART1-TXD	UART3-RXD
		PA9-->UART1-RXD	UART3-TXD
	手机蓝牙APP地面站	
		PA21-UART2-TXD	蓝牙串口模块RXD
		PA22-->UART2-RXD	蓝牙串口模块TXD
	US100超声波模块	
		PB2-UART3-TXD	US100-TX/TRIG
		PB3-->UART3-RXD	US100-RX/ECHO
	12路灰度传感器FPC	
	  PA31-->P1
		PA28-->P2
		PA1-->P3
		PA0-->P4
		PA25-->P5
		PA24-->P6
		PB24-->P7
		PB23-->P8
		PB19-->P9
		PB18-->P10
		PA16-->P11
		PB13-->P12
	电机控制MPWM	
		PA4-A0-PWM-CH3	  右边电机调速INA1
		PA7-->A0-PWM-CH2	右边电机调速INA2
		PA3-->A0-PWM-CH1	左边电机调速INB1
		PB14-->A0-PWM-CH0	左边电机调速INB2		
	舵机控制SPWM	
		PA15-A1-PWM-CH0	  预留1
		PB1-->A1-PWM-CH1	预留2
		PA23-->G7-PWM-CH0	预留3
		PA2-->G7-PWM-CH1	前轮舵机转向控制PWM
	编码器测速ENC	
		PB4-RIGHT-PULSE	  右边电机脉冲倍频输出P1
		PB5-->LEFT-PULSE	左边电机脉冲倍频输出P2
		PB6-->RIGHT-DIR	  右边电机脉冲鉴相输出D1
		PB7-->LEFT-DIR	  左边电机脉冲鉴相输出D2
	外置IMU接口IMU	
		PA29-I2C-SCL	MPU6050-SCL
		PA30-->I2C-SDA	MPU6050-SDA
		PB0-->HEATER	温控IO可选
	电池电压采集	
		PA26-ADC-VBAT	需要外部分压后才允许接入
****************************************************************************************/

#include "ti_msp_dl_config.h"
#include "headfile.h"
#include "../driver/ntimer.h"
#include "../driver/nuart.h"
#include "motor_app.h"
#include "../driver/encoder_driver.h"
#include "uart_app.h"
#include <stdio.h>
#include "pid.h"

int main(void)
{
	usart_irq_config();         //串口中断配置(必须在SYSCFG_DL_init之前)
  SYSCFG_DL_init();	      		//系统资源配置初始化
	ncontroller_set_priority(); //中断优先级设置
	OLED_Init();						    //显示屏初始化
	LED_App_Init();             //RGB LED应用层初始化
	Button_Init();              //五向按键初始化
	Buzzer_Init();              //蜂鸣器初始化
	UART0_App_Init();           //串口0应用层初始化
//	IMU_App_Init();             //IMU应用层初始化
	Motor_App_Init();           //电机控制应用层初始化
	Encoder_Init();             //编码器初始化
	timer_irq_config();

  static uint32_t speed_print_counter = 0;  // 速度打印计数器

  while(1)
  {
		get_wheel_speed();  //编码器速度更新
		screen_display();//屏幕显示（已集成五向按键页面翻页控制）
		uart_process();  //UART处理 - 老板专用一体化接口（接收回显）

		// ========== 简单速度打印 ==========
//		speed_print_counter++;
//		if(speed_print_counter >= 2000)  // 每2000个循环打印一次（约2秒）
//		{
//			// 构建简单的速度数据字符串
//			char speed_data[64];
//			int len = sprintf(speed_data, "L:%.1f R:%.1f RPM\r\n",
//			                  NEncoder.left_motor_speed_rpm,
//			                  NEncoder.right_motor_speed_rpm);
//			UART0_App_SendBytes((uint8_t*)speed_data, len);
//			speed_print_counter = 0;
//		}

		// ========== 电机控制演示 ==========
		// 基础运动演示
//		car_move(-500, 500);   // 前进


  }
}





