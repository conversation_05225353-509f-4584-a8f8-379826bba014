<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\ncontroller.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\ncontroller.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6210000: Last Updated: Wed Jul 30 12:13:57 2025
<BR><P>
<H3>Maximum Stack Usage =        236 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
TIMG12_IRQHandler &rArr; IMU_App_Update &rArr; ICM20608_Read_Data &rArr; i2creadnbyte &rArr; I2C_ReadReg &rArr; DL_I2C_fillControllerTXFIFO
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[3]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">SVC_Handler</a><BR>
 <LI><a href="#[4]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">PendSV_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[a]">ADC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[b]">ADC1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1b]">AES_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[c]">CANFD0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[d]">DAC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1d]">DMA_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[6]">GROUP0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[7]">GROUP1_IRQHandler</a> from interrupt_handler.o(.text.GROUP1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from system.o(.text.HardFault_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[19]">I2C0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1a]">I2C1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from system.o(.text.NMI_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[4]">PendSV_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1c]">RTC_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[e]">SPI0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[f]">SPI1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from system.o(.text.SysTick_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[15]">TIMA0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[16]">TIMA1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[13]">TIMG0_IRQHandler</a> from ntimer.o(.text.TIMG0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[18]">TIMG12_IRQHandler</a> from ntimer.o(.text.TIMG12_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[14]">TIMG6_IRQHandler</a> from ntimer.o(.text.TIMG6_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[17]">TIMG7_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[8]">TIMG8_IRQHandler</a> from ntimer.o(.text.TIMG8_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[12]">UART0_IRQHandler</a> from nuart.o(.text.UART0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[10]">UART1_IRQHandler</a> from nuart.o(.text.UART1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[11]">UART2_IRQHandler</a> from nuart.o(.text.UART2_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[9]">UART3_IRQHandler</a> from nuart.o(.text.UART3_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1e]">__main</a> from __main.o(!!!main) referenced from startup_mspm0g350x_uvision.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[1e]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(.text)
</UL>
<P><STRONG><a name="[1f]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[21]"></a>__scatterload_rt2</STRONG> (Thumb, 74 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[fc]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[fd]"></a>__scatterload_loop</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[22]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[fe]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, __scatter.o(!!handler_null), UNUSED)

<P><STRONG><a name="[ff]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[100]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))

<P><STRONG><a name="[23]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent_end
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[24]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
</UL>

<P><STRONG><a name="[29]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[101]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[102]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[103]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[104]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[105]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000034))

<P><STRONG><a name="[106]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[107]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[108]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[109]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[10a]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[10b]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[10c]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[10d]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[10e]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[10f]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[110]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000006))

<P><STRONG><a name="[111]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000010))

<P><STRONG><a name="[112]"></a>__rt_lib_init_relocate_pie_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[113]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000035))

<P><STRONG><a name="[114]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[115]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[116]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[2e]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[117]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[118]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[119]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[11a]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[11b]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[11c]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[11d]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[20]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[11e]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[26]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[28]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[11f]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[2a]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 192 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; uart_process &rArr; UART0_App_GetRxData &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[120]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[4c]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[2d]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[121]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[2f]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[37]"></a>__aeabi_memcpy4</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, rt_memcpy.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>

<P><STRONG><a name="[122]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy.o(.emb_text), UNUSED)

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>ADC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>AES_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>CANFD0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DAC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>Default_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>

<P><STRONG><a name="[6]"></a>GROUP0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>I2C0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIMA0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMG7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[25]"></a>_printf_int_dec</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_udiv10
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned (Weak Reference)
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_signed (Weak Reference)
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
</UL>

<P><STRONG><a name="[36]"></a>__aeabi_memcpy</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, rt_memcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_GetRxData
</UL>

<P><STRONG><a name="[123]"></a>__rt_memcpy</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, rt_memcpy.o(.text), UNUSED)

<P><STRONG><a name="[39]"></a>_memset_w</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr_w
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>

<P><STRONG><a name="[38]"></a>_memset</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[3a]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>

<P><STRONG><a name="[124]"></a>__rt_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[73]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_RxCallback
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_GetRxData
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
</UL>

<P><STRONG><a name="[125]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[3b]"></a>__rt_memclr_w</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[6e]"></a>__aeabi_uidivmod</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, aeabi_sdivfast.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
</UL>

<P><STRONG><a name="[126]"></a>__aeabi_idivmod</STRONG> (Thumb, 472 bytes, Stack size 8 bytes, aeabi_sdivfast.o(.text), UNUSED)

<P><STRONG><a name="[127]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[128]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[129]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[66]"></a>__aeabi_fdiv</STRONG> (Thumb, 0 bytes, Stack size 20 bytes, fdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_right_motor_speed
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_left_motor_speed
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cutoff_frequency
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
</UL>

<P><STRONG><a name="[3d]"></a>_fdiv</STRONG> (Thumb, 334 bytes, Stack size 20 bytes, fdiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frdiv
</UL>

<P><STRONG><a name="[3c]"></a>_frdiv</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fdiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
</UL>

<P><STRONG><a name="[cc]"></a>__aeabi_f2iz</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, ffixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[12a]"></a>_ffix</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, ffixi.o(.text), UNUSED)

<P><STRONG><a name="[75]"></a>__aeabi_f2uiz</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, ffixui.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
</UL>

<P><STRONG><a name="[12b]"></a>_ffixu</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, ffixui.o(.text), UNUSED)

<P><STRONG><a name="[3f]"></a>__aeabi_i2f_normalise</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
</UL>

<P><STRONG><a name="[3e]"></a>__aeabi_i2f</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f_normalise
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_right_motor_speed
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_left_motor_speed
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
</UL>

<P><STRONG><a name="[12c]"></a>_fflt</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflti.o(.text), UNUSED)

<P><STRONG><a name="[40]"></a>__aeabi_ui2f</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f_normalise
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Work
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_right_motor_speed
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_left_motor_speed
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
</UL>

<P><STRONG><a name="[12d]"></a>_ffltu</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflti.o(.text), UNUSED)

<P><STRONG><a name="[41]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[43]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
</UL>

<P><STRONG><a name="[35]"></a>_printf_int_common</STRONG> (Thumb, 176 bytes, Stack size 40 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding (Weak Reference)
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding (Weak Reference)
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[34]"></a>__rt_udiv10</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, rtudiv10.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[f6]"></a>_frnd</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, frnd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _frnd
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[f5]"></a>_fsqrt</STRONG> (Thumb, 140 bytes, Stack size 12 bytes, fsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _fsqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
</UL>

<P><STRONG><a name="[42]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[12e]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[12f]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[46]"></a>__fpl_fcmp_InfNaN</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, fcmpin.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN2
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpeq
</UL>

<P><STRONG><a name="[e5]"></a>__ARM_scalbnf</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, fscalbn.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_scalbnf
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
</UL>

<P><STRONG><a name="[130]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[49]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[131]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[27]"></a>__user_setup_stackheap</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[2c]"></a>exit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_call_atexit_fns (Weak Reference)
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[47]"></a>__fpl_cmpreturn</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, cmpret.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>

<P><STRONG><a name="[48]"></a>__fpl_fcheck_NaN2</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, fnan2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>

<P><STRONG><a name="[4d]"></a>__fpl_return_NaN</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, retnan.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN2
</UL>

<P><STRONG><a name="[30]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[132]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[133]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[134]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[d1]"></a>Button_Init</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, nbutton.o(.text.Button_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Button_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[99]"></a>Buzzer_Beep</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, buzzer_app.o(.text.Buzzer_Beep))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Button_State_One
</UL>

<P><STRONG><a name="[d2]"></a>Buzzer_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, buzzer_app.o(.text.Buzzer_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Buzzer_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4e]"></a>Buzzer_Work</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, buzzer_app.o(.text.Buzzer_Work))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Buzzer_Work &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmplt
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG6_IRQHandler
</UL>

<P><STRONG><a name="[dd]"></a>Calibration_Get_CRC32</STRONG> (Thumb, 248 bytes, Stack size 20 bytes, calibration.o(.text.Calibration_Get_CRC32))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Calibration_Get_CRC32
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[6a]"></a>Calibration_Get_Status</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, calibration.o(.text.Calibration_Get_Status))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Calibration_Status
</UL>

<P><STRONG><a name="[68]"></a>Calibration_Save</STRONG> (Thumb, 276 bytes, Stack size 20 bytes, calibration.o(.text.Calibration_Save))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Calibration_Save
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
</UL>

<P><STRONG><a name="[67]"></a>Calibration_Set_Gyro_Offset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, calibration.o(.text.Calibration_Set_Gyro_Offset))
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
</UL>

<P><STRONG><a name="[6f]"></a>Calibration_Update</STRONG> (Thumb, 316 bytes, Stack size 20 bytes, calibration.o(.text.Calibration_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Calibration_Update
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
</UL>

<P><STRONG><a name="[de]"></a>Calibration_Verify_Integrity</STRONG> (Thumb, 256 bytes, Stack size 20 bytes, calibration.o(.text.Calibration_Verify_Integrity))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Calibration_Verify_Integrity
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[9b]"></a>DL_ADC12_setClockConfig</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, dl_adc12.o(.text.DL_ADC12_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_ADC12_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
</UL>

<P><STRONG><a name="[be]"></a>DL_Common_delayCycles</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dl_common.o(.text.DL_Common_delayCycles))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[9d]"></a>DL_DMA_initChannel</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, dl_dma.o(.text.DL_DMA_initChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_DMA_initChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_CH0_init
</UL>

<P><STRONG><a name="[60]"></a>DL_I2C_fillControllerTXFIFO</STRONG> (Thumb, 128 bytes, Stack size 28 bytes, dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DL_I2C_fillControllerTXFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ReadReg
</UL>

<P><STRONG><a name="[61]"></a>DL_I2C_flushControllerTXFIFO</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_I2C_flushControllerTXFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ReadReg
</UL>

<P><STRONG><a name="[a0]"></a>DL_I2C_setClockConfig</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, dl_i2c.o(.text.DL_I2C_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_I2C_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[a9]"></a>DL_SPI_init</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, dl_spi.o(.text.DL_SPI_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_SPI_init
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_0_init
</UL>

<P><STRONG><a name="[a8]"></a>DL_SPI_setClockConfig</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, dl_spi.o(.text.DL_SPI_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_SPI_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_0_init
</UL>

<P><STRONG><a name="[ac]"></a>DL_SYSCTL_configSYSPLL</STRONG> (Thumb, 220 bytes, Stack size 40 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_SYSCTL_configSYSPLL
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[ab]"></a>DL_SYSCTL_setHFCLKSourceHFXTParams</STRONG> (Thumb, 100 bytes, Stack size 20 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_SYSCTL_setHFCLKSourceHFXTParams
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[ad]"></a>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[51]"></a>DL_TimerA_initPWMMode</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, dl_timer.o(.text.DL_TimerA_initPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[52]"></a>DL_Timer_initPWMMode</STRONG> (Thumb, 172 bytes, Stack size 20 bytes, dl_timer.o(.text.DL_Timer_initPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_Timer_initPWMMode
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
</UL>

<P><STRONG><a name="[af]"></a>DL_Timer_initTimerMode</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Timer_initTimerMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Timer_initTimerMode
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G12_init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G8_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G6_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G0_init
</UL>

<P><STRONG><a name="[a4]"></a>DL_Timer_setCaptCompUpdateMethod</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCaptCompUpdateMethod
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[a3]"></a>DL_Timer_setCaptureCompareOutCtl</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCaptureCompareOutCtl
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[94]"></a>DL_Timer_setCaptureCompareValue</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareValue))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_App_Init
</UL>

<P><STRONG><a name="[a2]"></a>DL_Timer_setClockConfig</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G12_init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G8_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G6_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G0_init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[b5]"></a>DL_UART_init</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_UART_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[c5]"></a>DL_UART_receiveDataCheck</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dl_uart.o(.text.DL_UART_receiveDataCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_receiveDataCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[b4]"></a>DL_UART_setClockConfig</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, dl_uart.o(.text.DL_UART_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[c4]"></a>DL_UART_transmitDataBlocking</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, dl_uart.o(.text.DL_UART_transmitDataBlocking))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_SendBytes
</UL>

<P><STRONG><a name="[53]"></a>Delay_Ms</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, system.o(.text.Delay_Ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Delay_Ms
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
</UL>

<P><STRONG><a name="[55]"></a>Draw_Logo</STRONG> (Thumb, 192 bytes, Stack size 32 bytes, oled.o(.text.Draw_Logo))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Draw_Logo &rArr; LCD_CLS &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[d4]"></a>Encoder_Init</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, encoder_driver.o(.text.Encoder_Init))
<BR><BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[59]"></a>FusionOffsetUpdate</STRONG> (Thumb, 192 bytes, Stack size 48 bytes, fusionoffset.o(.text.FusionOffsetUpdate))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = FusionOffsetUpdate &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpgt
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
</UL>

<P><STRONG><a name="[7]"></a>GROUP1_IRQHandler</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, interrupt_handler.o(.text.GROUP1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GROUP1_IRQHandler &rArr; QEI1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEI1_IRQHandler
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEI0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e0]"></a>HUI_Get_Pin_State</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, hui_app.o(.text.HUI_Get_Pin_State))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[df]"></a>HUI_Read_All_Pins</STRONG> (Thumb, 104 bytes, Stack size 0 bytes, hui_app.o(.text.HUI_Read_All_Pins))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system.o(.text.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>I2C_ReadReg</STRONG> (Thumb, 664 bytes, Stack size 48 bytes, ni2c.o(.text.I2C_ReadReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = I2C_ReadReg &rArr; DL_I2C_fillControllerTXFIFO
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_flushControllerTXFIFO
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_fillControllerTXFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2creadnbyte
</UL>

<P><STRONG><a name="[62]"></a>ICM20608_Calibrate_Gyro</STRONG> (Thumb, 324 bytes, Stack size 64 bytes, icm20608.o(.text.ICM20608_Calibrate_Gyro))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = ICM20608_Calibrate_Gyro &rArr; i2creadnbyte &rArr; I2C_ReadReg &rArr; DL_I2C_fillControllerTXFIFO
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calibration_Save
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calibration_Set_Gyro_Offset
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2creadnbyte
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Update
</UL>

<P><STRONG><a name="[8e]"></a>ICM20608_Get_Angles</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, icm20608.o(.text.ICM20608_Get_Angles))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Update
</UL>

<P><STRONG><a name="[77]"></a>ICM20608_Get_Calib_Status</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, icm20608.o(.text.ICM20608_Get_Calib_Status))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Calib_Status
</UL>

<P><STRONG><a name="[69]"></a>ICM20608_Get_Calibration_Status</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, icm20608.o(.text.ICM20608_Get_Calibration_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ICM20608_Get_Calibration_Status
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calibration_Get_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Calibration_Status_String
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Calibration_Status
</UL>

<P><STRONG><a name="[7b]"></a>ICM20608_Get_Fusion_Status</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, icm20608.o(.text.ICM20608_Get_Fusion_Status))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Status_String
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Results
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Fusion_Status
</UL>

<P><STRONG><a name="[7d]"></a>ICM20608_Get_Fusion_Timer</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, icm20608.o(.text.ICM20608_Get_Fusion_Timer))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Is_Stationary
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Fusion_Timer
</UL>

<P><STRONG><a name="[7f]"></a>ICM20608_Get_Gyro_Offset</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, icm20608.o(.text.ICM20608_Get_Gyro_Offset))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Gyro_Offset
</UL>

<P><STRONG><a name="[6b]"></a>ICM20608_Get_Temperature_Range</STRONG> (Thumb, 356 bytes, Stack size 64 bytes, icm20608.o(.text.ICM20608_Get_Temperature_Range))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = ICM20608_Get_Temperature_Range &rArr; __aeabi_fsub
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmplt
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpgt
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Temperature_Range
</UL>

<P><STRONG><a name="[81]"></a>ICM20608_Get_Yaw_Drift_Rate</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, icm20608.o(.text.ICM20608_Get_Yaw_Drift_Rate))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Performance_Stats
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Status_String
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Results
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Yaw_Drift_Rate
</UL>

<P><STRONG><a name="[82]"></a>ICM20608_Get_Yaw_Quality</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, icm20608.o(.text.ICM20608_Get_Yaw_Quality))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Performance_Stats
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Status_String
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Results
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Yaw_Status_String
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Yaw_Quality
</UL>

<P><STRONG><a name="[83]"></a>ICM20608_Is_Temperature_Stable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, icm20608.o(.text.ICM20608_Is_Temperature_Stable))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Performance_Stats
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Status_String
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Results
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_System_Status
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Is_Temperature_Stable
</UL>

<P><STRONG><a name="[6c]"></a>ICM20608_Read_Data</STRONG> (Thumb, 880 bytes, Stack size 96 bytes, icm20608.o(.text.ICM20608_Read_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = ICM20608_Read_Data &rArr; i2creadnbyte &rArr; I2C_ReadReg &rArr; DL_I2C_fillControllerTXFIFO
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calibration_Update
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FusionOffsetUpdate
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPButterworth
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2creadnbyte
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmplt
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpgt
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Update
</UL>

<P><STRONG><a name="[70]"></a>ICM20608_Update_Angles</STRONG> (Thumb, 908 bytes, Stack size 64 bytes, icm20608.o(.text.ICM20608_Update_Angles))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = ICM20608_Update_Angles &rArr; atan2f &rArr; __mathlib_flt_infnan2 &rArr; __aeabi_fadd
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmplt
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpgt
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Update
</UL>

<P><STRONG><a name="[db]"></a>IMU_App_Get_Angles</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, imu_app.o(.text.IMU_App_Get_Angles))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[76]"></a>IMU_App_Get_Calib_Status</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Calib_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Get_Calib_Status
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Calib_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[78]"></a>IMU_App_Get_Calibration_Status</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Calibration_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IMU_App_Get_Calibration_Status &rArr; ICM20608_Get_Calibration_Status
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Calibration_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[79]"></a>IMU_App_Get_Calibration_Status_String</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Calibration_Status_String))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IMU_App_Get_Calibration_Status_String &rArr; ICM20608_Get_Calibration_Status
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Calibration_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[7a]"></a>IMU_App_Get_Fusion_Status</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Fusion_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Get_Fusion_Status
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Fusion_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[7c]"></a>IMU_App_Get_Fusion_Timer</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Fusion_Timer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Get_Fusion_Timer
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Fusion_Timer
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[7e]"></a>IMU_App_Get_Gyro_Offset</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Gyro_Offset))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Get_Gyro_Offset
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Gyro_Offset
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[80]"></a>IMU_App_Get_Performance_Stats</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, imu_app.o(.text.IMU_App_Get_Performance_Stats))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = IMU_App_Get_Performance_Stats
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Yaw_Drift_Rate
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Yaw_Quality
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Is_Temperature_Stable
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[84]"></a>IMU_App_Get_System_Status</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_System_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Get_System_Status
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Is_Temperature_Stable
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[dc]"></a>IMU_App_Get_Temperature</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, imu_app.o(.text.IMU_App_Get_Temperature))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[85]"></a>IMU_App_Get_Temperature_Range</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Temperature_Range))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = IMU_App_Get_Temperature_Range &rArr; ICM20608_Get_Temperature_Range &rArr; __aeabi_fsub
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Temperature_Range
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[86]"></a>IMU_App_Get_Test_Results</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, imu_app.o(.text.IMU_App_Get_Test_Results))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = IMU_App_Get_Test_Results &rArr; __aeabi_fcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Yaw_Drift_Rate
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Yaw_Quality
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Is_Temperature_Stable
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Fusion_Status
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[87]"></a>IMU_App_Get_Test_Status_String</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, imu_app.o(.text.IMU_App_Get_Test_Status_String))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = IMU_App_Get_Test_Status_String &rArr; __aeabi_fcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Yaw_Drift_Rate
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Yaw_Quality
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Is_Temperature_Stable
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Fusion_Status
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[88]"></a>IMU_App_Get_Yaw_Drift_Rate</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Yaw_Drift_Rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Get_Yaw_Drift_Rate
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Yaw_Drift_Rate
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[89]"></a>IMU_App_Get_Yaw_Quality</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Yaw_Quality))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Get_Yaw_Quality
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Yaw_Quality
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[8a]"></a>IMU_App_Get_Yaw_Status_String</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Get_Yaw_Status_String))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Get_Yaw_Status_String
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Yaw_Quality
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[da]"></a>IMU_App_Is_Ready</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, imu_app.o(.text.IMU_App_Is_Ready))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[8b]"></a>IMU_App_Is_Stationary</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Is_Stationary))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Is_Stationary
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Fusion_Timer
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[8c]"></a>IMU_App_Is_Temperature_Stable</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, imu_app.o(.text.IMU_App_Is_Temperature_Stable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IMU_App_Is_Temperature_Stable
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Is_Temperature_Stable
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[8d]"></a>IMU_App_Update</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, imu_app.o(.text.IMU_App_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = IMU_App_Update &rArr; ICM20608_Read_Data &rArr; i2creadnbyte &rArr; I2C_ReadReg &rArr; DL_I2C_fillControllerTXFIFO
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Angles
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG12_IRQHandler
</UL>

<P><STRONG><a name="[58]"></a>LCD_CLS</STRONG> (Thumb, 364 bytes, Stack size 32 bytes, oled.o(.text.LCD_CLS))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LCD_CLS &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
</UL>

<P><STRONG><a name="[8f]"></a>LCD_P6x8Char</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, oled.o(.text.LCD_P6x8Char))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_P6x8Char &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[90]"></a>LCD_P6x8Str</STRONG> (Thumb, 136 bytes, Stack size 32 bytes, oled.o(.text.LCD_P6x8Str))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LCD_P6x8Str &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_string
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[91]"></a>LCD_clear_L</STRONG> (Thumb, 232 bytes, Stack size 32 bytes, oled.o(.text.LCD_clear_L))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LCD_clear_L &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[d0]"></a>LED_App_Init</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, led_app.o(.text.LED_App_Init))
<BR><BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6d]"></a>LPButterworth</STRONG> (Thumb, 324 bytes, Stack size 64 bytes, filter.o(.text.LPButterworth))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = LPButterworth &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpeq
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_right_motor_speed
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_left_motor_speed
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
</UL>

<P><STRONG><a name="[93]"></a>Motor_App_Init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, motor_app.o(.text.Motor_App_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Motor_App_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system.o(.text.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[95]"></a>OLED_Init</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, oled.o(.text.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = OLED_Init &rArr; Draw_Logo &rArr; LCD_CLS &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_begin
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[56]"></a>OLED_WrCmd</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, oled.o(.text.OLED_WrCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_begin
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_clear_L
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Char
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
</UL>

<P><STRONG><a name="[57]"></a>OLED_WrDat</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, oled.o(.text.OLED_WrDat))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Char
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
</UL>

<P><STRONG><a name="[5e]"></a>QEI0_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, encoder_driver.o(.text.QEI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = QEI0_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[5d]"></a>QEI1_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, encoder_driver.o(.text.QEI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = QEI1_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[97]"></a>Read_Button_State_One</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, nbutton.o(.text.Read_Button_State_One))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Read_Button_State_One &rArr; millis
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Beep
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;millis
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_button_state_all
</UL>

<P><STRONG><a name="[9a]"></a>SYSCFG_DL_ADC12_0_init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_ADC12_0_init &rArr; DL_ADC12_setClockConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[9c]"></a>SYSCFG_DL_DMA_CH0_init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_DMA_CH0_init &rArr; DL_DMA_initChannel
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_initChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_init
</UL>

<P><STRONG><a name="[9e]"></a>SYSCFG_DL_DMA_init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SYSCFG_DL_DMA_init &rArr; SYSCFG_DL_DMA_CH0_init &rArr; DL_DMA_initChannel
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_CH0_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[bb]"></a>SYSCFG_DL_GPIO_init</STRONG> (Thumb, 284 bytes, Stack size 20 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SYSCFG_DL_GPIO_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[9f]"></a>SYSCFG_DL_I2C_0_init</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_I2C_0_init &rArr; DL_I2C_setClockConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[a1]"></a>SYSCFG_DL_PWM_0_init</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = SYSCFG_DL_PWM_0_init &rArr; DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[a5]"></a>SYSCFG_DL_PWM_1_init</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = SYSCFG_DL_PWM_1_init &rArr; DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[a6]"></a>SYSCFG_DL_PWM_2_init</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = SYSCFG_DL_PWM_2_init &rArr; DL_Timer_initPWMMode
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[a7]"></a>SYSCFG_DL_SPI_0_init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_SPI_0_init &rArr; DL_SPI_init
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setClockConfig
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[bd]"></a>SYSCFG_DL_SYSCTL_CLK_init</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[aa]"></a>SYSCFG_DL_SYSCTL_init</STRONG> (Thumb, 144 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SYSCFG_DL_SYSCTL_init &rArr; DL_SYSCTL_configSYSPLL
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[bc]"></a>SYSCFG_DL_SYSTICK_init</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[ae]"></a>SYSCFG_DL_TIMER_G0_init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_TIMER_G0_init &rArr; DL_Timer_initTimerMode
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b0]"></a>SYSCFG_DL_TIMER_G12_init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_TIMER_G12_init &rArr; DL_Timer_initTimerMode
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b1]"></a>SYSCFG_DL_TIMER_G6_init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G6_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_TIMER_G6_init &rArr; DL_Timer_initTimerMode
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b2]"></a>SYSCFG_DL_TIMER_G8_init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_TIMER_G8_init &rArr; DL_Timer_initTimerMode
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b3]"></a>SYSCFG_DL_UART_0_init</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_UART_0_init &rArr; DL_UART_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b6]"></a>SYSCFG_DL_UART_1_init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SYSCFG_DL_UART_1_init &rArr; DL_UART_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b7]"></a>SYSCFG_DL_UART_2_init</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SYSCFG_DL_UART_2_init &rArr; DL_UART_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b8]"></a>SYSCFG_DL_UART_3_init</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_UART_3_init &rArr; DL_UART_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b9]"></a>SYSCFG_DL_init</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = SYSCFG_DL_init &rArr; SYSCFG_DL_PWM_0_init &rArr; DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_CLK_init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_0_init
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G12_init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G8_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G6_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G0_init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ba]"></a>SYSCFG_DL_initPower</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_initPower))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_initPower
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system.o(.text.SysTick_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMG0_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ntimer.o(.text.TIMG0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TIMG0_IRQHandler &rArr; get_systime &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMG12_IRQHandler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ntimer.o(.text.TIMG12_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = TIMG12_IRQHandler &rArr; IMU_App_Update &rArr; ICM20608_Read_Data &rArr; i2creadnbyte &rArr; I2C_ReadReg &rArr; DL_I2C_fillControllerTXFIFO
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Update
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMG6_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, ntimer.o(.text.TIMG6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIMG6_IRQHandler &rArr; Buzzer_Work &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Work
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>TIMG8_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ntimer.o(.text.TIMG8_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TIMG8_IRQHandler &rArr; get_systime &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c0]"></a>UART0_App_GetRxData</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, uart_app.o(.text.UART0_App_GetRxData))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART0_App_GetRxData &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_process
</UL>

<P><STRONG><a name="[d3]"></a>UART0_App_Init</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, uart_app.o(.text.UART0_App_Init))
<BR><BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e2]"></a>UART0_App_IsRxComplete</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart_app.o(.text.UART0_App_IsRxComplete))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_process
</UL>

<P><STRONG><a name="[c1]"></a>UART0_App_RxCallback</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, uart_app.o(.text.UART0_App_RxCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART0_App_RxCallback &rArr; millis
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;millis
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[c2]"></a>UART0_App_RxProcess</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, uart_app.o(.text.UART0_App_RxProcess))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART0_App_RxProcess &rArr; millis
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;millis
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_process
</UL>

<P><STRONG><a name="[c3]"></a>UART0_App_SendBytes</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, uart_app.o(.text.UART0_App_SendBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART0_App_SendBytes
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_transmitDataBlocking
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_process
</UL>

<P><STRONG><a name="[12]"></a>UART0_IRQHandler</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, nuart.o(.text.UART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART0_IRQHandler &rArr; UART0_App_RxCallback &rArr; millis
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_RxCallback
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_receiveDataCheck
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>UART1_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nuart.o(.text.UART1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>UART2_IRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, nuart.o(.text.UART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = UART2_IRQHandler &rArr; get_systime &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UART3_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nuart.o(.text.UART3_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>delay_ms</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, system.o(.text.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
</UL>

<P><STRONG><a name="[c6]"></a>display_6_8_number</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, oled.o(.text.display_6_8_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = display_6_8_number &rArr; write_6_8_number &rArr; LCD_P6x8Str &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[c8]"></a>display_6_8_string</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, oled.o(.text.display_6_8_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = display_6_8_string &rArr; LCD_P6x8Str &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[d9]"></a>get_key_short_press</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, nbutton.o(.text.get_key_short_press))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[c9]"></a>get_left_motor_speed</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, encoder_driver.o(.text.get_left_motor_speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = get_left_motor_speed &rArr; set_cutoff_frequency &rArr; tanf &rArr; __mathlib_rredf2 &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPButterworth
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cutoff_frequency
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_wheel_speed
</UL>

<P><STRONG><a name="[cb]"></a>get_right_motor_speed</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, encoder_driver.o(.text.get_right_motor_speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = get_right_motor_speed &rArr; LPButterworth &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPButterworth
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_wheel_speed
</UL>

<P><STRONG><a name="[bf]"></a>get_systime</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, system.o(.text.get_systime))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = get_systime &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG12_IRQHandler
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG8_IRQHandler
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG6_IRQHandler
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[cd]"></a>get_wheel_speed</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, encoder_driver.o(.text.get_wheel_speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = get_wheel_speed &rArr; get_left_motor_speed &rArr; set_cutoff_frequency &rArr; tanf &rArr; __mathlib_rredf2 &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_right_motor_speed
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_left_motor_speed
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[64]"></a>i2creadnbyte</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, ni2c.o(.text.i2creadnbyte))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = i2creadnbyte &rArr; I2C_ReadReg &rArr; DL_I2C_fillControllerTXFIFO
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ReadReg
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
</UL>

<P><STRONG><a name="[2b]"></a>main</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = main &rArr; uart_process &rArr; UART0_App_GetRxData &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_process
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_wheel_speed
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_irq_config
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_Init
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_App_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_Init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Button_Init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_App_Init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ncontroller_set_priority
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_irq_config
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[98]"></a>millis</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, system.o(.text.millis))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = millis
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_RxCallback
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_RxProcess
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Button_State_One
</UL>

<P><STRONG><a name="[cf]"></a>ncontroller_set_priority</STRONG> (Thumb, 108 bytes, Stack size 0 bytes, system.o(.text.ncontroller_set_priority))
<BR><BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d8]"></a>read_button_state_all</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, nbutton.o(.text.read_button_state_all))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = read_button_state_all &rArr; Read_Button_State_One &rArr; millis
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Button_State_One
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
</UL>

<P><STRONG><a name="[d6]"></a>screen_display</STRONG> (Thumb, 3660 bytes, Stack size 72 bytes, ui.o(.text.screen_display))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = screen_display &rArr; display_6_8_number &rArr; write_6_8_number &rArr; LCD_P6x8Str &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HUI_Get_Pin_State
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HUI_Read_All_Pins
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_key_short_press
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_button_state_all
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Is_Stationary
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Performance_Stats
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Status_String
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Results
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calibration_Verify_Integrity
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calibration_Get_CRC32
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Calibration_Status_String
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Yaw_Status_String
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Yaw_Drift_Rate
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Calibration_Status
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Temperature_Range
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Gyro_Offset
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Fusion_Timer
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Fusion_Status
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Yaw_Quality
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_System_Status
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Is_Temperature_Stable
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Calib_Status
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Temperature
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Angles
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Is_Ready
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_string
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_number
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_clear_L
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ca]"></a>set_cutoff_frequency</STRONG> (Thumb, 168 bytes, Stack size 32 bytes, filter.o(.text.set_cutoff_frequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = set_cutoff_frequency &rArr; tanf &rArr; __mathlib_rredf2 &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_left_motor_speed
</UL>

<P><STRONG><a name="[96]"></a>ssd1306_begin</STRONG> (Thumb, 264 bytes, Stack size 24 bytes, ssd1306.o(.text.ssd1306_begin))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ssd1306_begin &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[d5]"></a>timer_irq_config</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, ntimer.o(.text.timer_irq_config))
<BR><BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d7]"></a>uart_process</STRONG> (Thumb, 42 bytes, Stack size 144 bytes, uart_process.o(.text.uart_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = uart_process &rArr; UART0_App_GetRxData &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_IsRxComplete
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_GetRxData
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_RxProcess
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_App_SendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ce]"></a>usart_irq_config</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, nuart.o(.text.usart_irq_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_irq_config
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c7]"></a>write_6_8_number</STRONG> (Thumb, 676 bytes, Stack size 56 bytes, oled.o(.text.write_6_8_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = write_6_8_number &rArr; LCD_P6x8Str &rArr; OLED_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Char
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmplt
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idiv
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_number
</UL>

<P><STRONG><a name="[54]"></a>__aeabi_uidiv</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, aeabi_sdivfast.o(.text_divfast))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;millis
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[e3]"></a>__aeabi_idiv</STRONG> (Thumb, 434 bytes, Stack size 8 bytes, aeabi_sdivfast.o(.text_divfast))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_idiv
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[ea]"></a>__ARM_common_ll_muluu</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, rredf.o(i.__ARM_common_ll_muluu))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_common_ll_muluu
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
</UL>

<P><STRONG><a name="[f4]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
</UL>

<P><STRONG><a name="[e4]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __mathlib_flt_infnan &rArr; __ARM_scalbnf
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[e6]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __mathlib_flt_infnan2 &rArr; __aeabi_fadd
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
</UL>

<P><STRONG><a name="[e7]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = __mathlib_flt_invalid &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[e8]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __mathlib_flt_underflow &rArr; __ARM_scalbnf
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
</UL>

<P><STRONG><a name="[e9]"></a>__mathlib_rredf2</STRONG> (Thumb, 366 bytes, Stack size 48 bytes, rredf.o(i.__mathlib_rredf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = __mathlib_rredf2 &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_common_ll_muluu
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[92]"></a>__aeabi_fcmpeq</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._feq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPButterworth
</UL>

<P><STRONG><a name="[ec]"></a>_feq</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._feq), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpeq
</UL>

<P><STRONG><a name="[65]"></a>__aeabi_fcmpge</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fgeq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[ee]"></a>_fgeq</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fgeq), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[5b]"></a>__aeabi_fcmpgt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fgr))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmpgt
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FusionOffsetUpdate
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Temperature_Range
</UL>

<P><STRONG><a name="[f0]"></a>_fgr</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fgr), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[74]"></a>__aeabi_fcmple</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fleq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Status_String
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_App_Get_Test_Results
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cutoff_frequency
</UL>

<P><STRONG><a name="[f1]"></a>_fleq</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, fcmp.o(i._fleq), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
</UL>

<P><STRONG><a name="[50]"></a>__aeabi_fcmplt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fls))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmplt
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Work
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Temperature_Range
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[f3]"></a>_fls</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fls), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
</UL>

<P><STRONG><a name="[72]"></a>atan2f</STRONG> (Thumb, 592 bytes, Stack size 48 bytes, atan2f.o(i.atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = atan2f &rArr; __mathlib_flt_infnan2 &rArr; __aeabi_fadd
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
</UL>

<P><STRONG><a name="[71]"></a>sqrtf</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, sqrtf.o(i.sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = sqrtf &rArr; _fsqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsqrt
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
</UL>

<P><STRONG><a name="[e1]"></a>tanf</STRONG> (Thumb, 312 bytes, Stack size 32 bytes, tanf.o(i.tanf))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = tanf &rArr; __mathlib_rredf2 &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frnd
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cutoff_frequency
</UL>

<P><STRONG><a name="[5c]"></a>__aeabi_fadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FusionOffsetUpdate
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPButterworth
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cutoff_frequency
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
</UL>

<P><STRONG><a name="[f7]"></a>_fadd</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
</UL>

<P><STRONG><a name="[135]"></a>__aeabi_cfcmpeq</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, feqf.o(x$fpl$feqf), UNUSED)

<P><STRONG><a name="[ed]"></a>_fcmpeq</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, feqf.o(x$fpl$feqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_feq
</UL>

<P><STRONG><a name="[ef]"></a>_fcmpge</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, fgef.o(x$fpl$fgeqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fgr
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fgeq
</UL>

<P><STRONG><a name="[136]"></a>__aeabi_cfcmple</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, flef.o(x$fpl$fleqf), UNUSED)

<P><STRONG><a name="[f2]"></a>_fcmple</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, flef.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fls
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fleq
</UL>

<P><STRONG><a name="[4f]"></a>__aeabi_fmul</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Work
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_display
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_right_motor_speed
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_left_motor_speed
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FusionOffsetUpdate
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPButterworth
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Calibrate_Gyro
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cutoff_frequency
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
</UL>

<P><STRONG><a name="[137]"></a>_fmul</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul), UNUSED)

<P><STRONG><a name="[eb]"></a>__aeabi_frsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub.o(x$fpl$frsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_frsub
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
</UL>

<P><STRONG><a name="[f9]"></a>_frsb</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, faddsub.o(x$fpl$frsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
</UL>

<P><STRONG><a name="[5a]"></a>__aeabi_fsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Update_Angles
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FusionOffsetUpdate
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPButterworth
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Read_Data
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Get_Temperature_Range
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2f
</UL>

<P><STRONG><a name="[fb]"></a>_fsub</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[fa]"></a>_fadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub.o(x$fpl$fadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frsb
</UL>

<P><STRONG><a name="[f8]"></a>_fsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub.o(x$fpl$fsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frsb
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3>
<P><STRONG><a name="[4b]"></a>_call_atexit_fns</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[45]"></a>_printf_post_padding</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[44]"></a>_printf_pre_padding</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[32]"></a>_printf_truncate_signed</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[33]"></a>_printf_truncate_unsigned</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>
<HR></body></html>
