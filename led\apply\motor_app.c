#include "motor_app.h"
#include "../driver/ntimer.h"

/**
 * @brief 电机应用层初始化
 */
void Motor_App_Init(void) {
    // PWM已在SYSCFG_DL_init()中初始化，这里只需要停止所有电机
    car_stop();
}

/**
 * @brief 左轮前进
 * @param speed 速度值 (0-999)
 */
void left_wheel_forward(int16_t speed) {
    // 限幅保护
    if (speed > 999) speed = 999;
    if (speed < 0) speed = 0;

    // 左电机前进：INB1=0, INB2=PWM值（调整方向）
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, left_pwm_channel_1);     // PA3=0
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, speed, left_pwm_channel_2); // PB14=PWM
}

/**
 * @brief 左轮后退
 * @param speed 速度值 (0-999)
 */
void left_wheel_backward(int16_t speed) {
    // 限幅保护
    if (speed > 999) speed = 999;
    if (speed < 0) speed = 0;

    // 左电机后退：INB1=PWM值, INB2=0（调整方向）
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, speed, left_pwm_channel_1); // PA3=PWM
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, left_pwm_channel_2);     // PB14=0
}

/**
 * @brief 右轮前进
 * @param speed 速度值 (0-999)
 */
void right_wheel_forward(int16_t speed) {
    // 限幅保护
    if (speed > 999) speed = 999;
    if (speed < 0) speed = 0;

    // 右电机前进：INA1=PWM值, INA2=0（调整方向）
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, speed, right_pwm_channel_1); // PA4=PWM
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, right_pwm_channel_2);     // PA7=0
}

/**
 * @brief 右轮后退
 * @param speed 速度值 (0-999)
 */
void right_wheel_backward(int16_t speed) {
    // 限幅保护
    if (speed > 999) speed = 999;
    if (speed < 0) speed = 0;

    // 右电机后退：INA1=0, INA2=PWM值（调整方向）
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, right_pwm_channel_1);     // PA4=0
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, speed, right_pwm_channel_2); // PA7=PWM
}

/**
 * @brief 整车停止
 */
void car_stop(void) {
    // 所有PWM输出设为0
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, left_pwm_channel_1);  // PA3=0
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, left_pwm_channel_2);  // PB14=0
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, right_pwm_channel_1); // PA4=0
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, right_pwm_channel_2); // PA7=0
}

/**
 * @brief 整车运动控制
 * @param left_speed 左轮速度 (-999到999，负值表示后退)
 * @param right_speed 右轮速度 (-999到999，负值表示后退)
 */
void car_move(int16_t left_speed, int16_t right_speed) {
    // 左轮控制
    if (left_speed > 0) {
        left_wheel_forward(left_speed);
    } else if (left_speed < 0) {
        left_wheel_backward(-left_speed);  // 取绝对值
    } else {
        // 左轮停止
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, left_pwm_channel_1);  // PA3=0
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, left_pwm_channel_2);  // PB14=0
    }
    
    // 右轮控制
    if (right_speed > 0) {
        right_wheel_forward(right_speed);
    } else if (right_speed < 0) {
        right_wheel_backward(-right_speed);  // 取绝对值
    } else {
        // 右轮停止
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, right_pwm_channel_1); // PA4=0
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, right_pwm_channel_2); // PA7=0
    }
}

/**
 * @brief 电机演示函数
 */
void motor_demo(void) {
    // 前进演示
    car_move(500, 500);
    delay_ms(2000);
    
    // 停止
    car_stop();
    delay_ms(1000);
    
    // 后退演示
    car_move(-400, -400);
    delay_ms(2000);
    
    // 停止
    car_stop();
    delay_ms(1000);
    
    // 左转演示
    car_move(-300, 300);
    delay_ms(1500);
    
    // 停止
    car_stop();
    delay_ms(1000);
    
    // 右转演示
    car_move(300, -300);
    delay_ms(1500);
    
    // 停止
    car_stop();
    delay_ms(1000);
    
    // 单轮控制演示
    left_wheel_forward(600);
    delay_ms(1000);
    
    car_stop();
    delay_ms(500);
    
    right_wheel_forward(600);
    delay_ms(1000);
    
    car_stop();
    delay_ms(500);
    
    left_wheel_backward(600);
    delay_ms(1000);
    
    car_stop();
    delay_ms(500);
    
    right_wheel_backward(600);
    delay_ms(1000);
    
    // 最终停止
    car_stop();
}
