#include "headfile.h"
#include "ui.h"
#include "imu_app.h"
#include "../driver/encoder_driver.h"
#include "../user/hui_app.h"

int16_t page_number=0;
static uint32_t display_counter = 0;

#define Page_Number_Max 10

/***************************************************
函数名: void Key_Scan(void)
说明:	按键扫描 - 五向按键控制页面翻页
入口:	无
出口:	无
备注:	右键增加页数，左键减少页数
作者:	无名创新
****************************************************/
void Key_Scan(void)
{
	// 读取按键状态
	read_button_state_all();

	// 获取短按按键值
	uint8_t key_val = get_key_short_press();

	if(key_val == 4) // 右键：页数增加
	{
		page_number++;
		if(page_number >= Page_Number_Max) page_number = 0; // 循环到第一页
		LCD_CLS(); // 清屏
	}
	else if(key_val == 3) // 左键：页数减少
	{
		page_number--;
		if(page_number < 0) page_number = Page_Number_Max - 1; // 循环到最后一页
		LCD_CLS(); // 清屏
	}

	// 保持计数器用于其他显示用途
	display_counter++;
}

/***************************************************
函数名: void screen_display(void)
说明:	屏幕显示刷新 - 简化版本
入口:	无
出口:	无
备注:	无
作者:	无名创新
****************************************************/
void screen_display(void)
{
	Key_Scan();
	switch(page_number)
	{
		case 0:
		{
			// 显示基本信息页面
			display_6_8_string(0,0,"LED Display Test");
			display_6_8_string(0,1,"Page: 1/10  L<->R");
			display_6_8_string(0,2,"MSPM0G3507");
			display_6_8_string(0,3,"OLED Working");
			display_6_8_string(0,4,"Font: 6x8");
			display_6_8_string(0,5,"Resolution: 128x64");
			display_6_8_string(0,6,"Driver: SSD1306");
			display_6_8_string(0,7,"Use L/R Key Switch");
			break;
		}
		case 1:
		{
			// 显示IMU姿态角页面
			euler_angles angles;
			float temp;
			uint8_t calib_status;

			display_6_8_string(0,0,"IMU Attitude");
			display_6_8_string(0,1,"Page: 2/10  L<->R");

			if(IMU_App_Is_Ready())
			{
				IMU_App_Get_Angles(&angles);
				temp = IMU_App_Get_Temperature();
				calib_status = IMU_App_Get_Calib_Status();
				uint8_t temp_stable = IMU_App_Is_Temperature_Stable();
				uint8_t system_status = IMU_App_Get_System_Status();

				display_6_8_string(0,2,"Roll: ");
				LCD_clear_L(36,2);  // 清除数字显示区域避免残留
				display_6_8_number(36,2,(int16_t)angles.roll);
				display_6_8_string(72,2,"deg");

				display_6_8_string(0,3,"Pitch:");
				LCD_clear_L(36,3);  // 清除数字显示区域避免残留
				display_6_8_number(36,3,(int16_t)angles.pitch);
				display_6_8_string(72,3,"deg");

				display_6_8_string(0,4,"Yaw:  ");
				LCD_clear_L(36,4);  // 清除数字显示区域避免残留
				display_6_8_number(36,4,(int16_t)angles.yaw);
				display_6_8_string(72,4,"deg");

				// 显示yaw值质量指示
				uint32_t yaw_quality = IMU_App_Get_Yaw_Quality();
				if(yaw_quality >= 90)
				{
					display_6_8_string(96,4,"A"); // Excellent
				}
				else if(yaw_quality >= 75)
				{
					display_6_8_string(96,4,"B"); // Good
				}
				else if(yaw_quality >= 60)
				{
					display_6_8_string(96,4,"C"); // Fair
				}
				else if(yaw_quality >= 40)
				{
					display_6_8_string(96,4,"D"); // Poor
				}
				else
				{
					display_6_8_string(96,4,"F"); // Bad
				}

				display_6_8_string(0,5,"Temp: ");
				LCD_clear_L(36,5);  // 清除数字显示区域避免残留
				display_6_8_number(36,5,(int16_t)temp);
				display_6_8_string(60,5,"C");

				// 显示温度稳定状态
				if(temp_stable)
				{
					display_6_8_string(84,5,"S"); // Stable
				}
				else
				{
					display_6_8_string(84,5,"U"); // Unstable
				}

				// 显示系统状态
				display_6_8_string(0,6,"Sys: ");
				switch(system_status)
				{
					case 0: display_6_8_string(30,6,"Init"); break;
					case 1: display_6_8_string(30,6,"TempWait"); break;
					case 2: display_6_8_string(30,6,"Ready"); break;
					default: display_6_8_string(30,6,"Error"); break;
				}
			}
			else
			{
				display_6_8_string(0,2,"IMU: Not Ready");
				display_6_8_string(0,3,"Check Connection");
				display_6_8_string(0,4,"I2C Address: 0x68");
				display_6_8_string(0,6,"Cal: Waiting");
			}
			display_6_8_string(0,7,"ICM20608 Sensor");
			break;
		}
		case 2:
		{
			// 显示IMU校准信息页面
			vector3f gyro_offset;
			uint8_t calib_status, fusion_status;
			uint32_t fusion_timer;

			display_6_8_string(0,0,"IMU Calibration");
			display_6_8_string(0,1,"Page: 3/10  L<->R");

			if(IMU_App_Is_Ready())
			{
				calib_status = IMU_App_Get_Calib_Status();
				fusion_status = IMU_App_Get_Fusion_Status();
				fusion_timer = IMU_App_Get_Fusion_Timer();
				IMU_App_Get_Gyro_Offset(&gyro_offset);
				uint8_t temp_stable = IMU_App_Is_Temperature_Stable();
				float temp_range = IMU_App_Get_Temperature_Range();
				calib_status_t calib_data_status = IMU_App_Get_Calibration_Status();

				// 显示校准方法
				display_6_8_string(0,2,"Method: ");
				if(fusion_status)
				{
					display_6_8_string(48,2,"Fusion");
				}
				else
				{
					display_6_8_string(48,2,"Simple");
				}

				// 显示温度稳定状态
				display_6_8_string(0,3,"Temp: ");
				if(temp_stable)
				{
					display_6_8_string(36,3,"Stable");
				}
				else
				{
					display_6_8_string(36,3,"Unstable");
				}

				// 显示温度变化范围
				display_6_8_string(72,3,"R:");
				if(temp_range < 99.0f)
				{
					display_6_8_number(84,3,(int16_t)(temp_range * 10)); // 0.1°C精度
				}
				else
				{
					display_6_8_string(84,3,"--");
				}

				// 显示零偏值
				display_6_8_string(0,4,"X: ");
				LCD_clear_L(18,4);  // 清除数字显示区域避免残留
				display_6_8_number(18,4,(int16_t)(gyro_offset.x * 100));
				display_6_8_string(0,5,"Y: ");
				LCD_clear_L(18,5);  // 清除数字显示区域避免残留
				display_6_8_number(18,5,(int16_t)(gyro_offset.y * 100));
				display_6_8_string(0,6,"Z: ");
				LCD_clear_L(18,6);  // 清除数字显示区域避免残留
				display_6_8_number(18,6,(int16_t)(gyro_offset.z * 100));

				// 显示静止状态和计时器
				display_6_8_string(72,4,"M:");
				if(IMU_App_Is_Stationary())
				{
					display_6_8_string(84,4,"S"); // Still
				}
				else
				{
					display_6_8_string(84,4,"M"); // Moving
				}

				display_6_8_string(72,5,"T:");
				LCD_clear_L(84,5);  // 清除数字显示区域避免残留
				display_6_8_number(84,5,(int16_t)(fusion_timer/200)); // 转换为秒

				// 显示校准数据状态
				display_6_8_string(0,7,"Data: ");
				switch(calib_data_status)
				{
					case CALIB_STATUS_VALID: display_6_8_string(36,7,"Saved"); break;
					case CALIB_STATUS_NONE: display_6_8_string(36,7,"None"); break;
					case CALIB_STATUS_INVALID: display_6_8_string(36,7,"Invalid"); break;
					case CALIB_STATUS_SAVING: display_6_8_string(36,7,"Saving"); break;
					default: display_6_8_string(36,7,"Error"); break;
				}
			}
			else
			{
				display_6_8_string(0,2,"IMU: Not Ready");
				display_6_8_string(0,3,"Cannot Calibrate");
				display_6_8_string(0,4,"Check Connection");
			}
			break;
		}
		case 3:
		{
			// 显示yaw值详细信息页面
			display_6_8_string(0,0,"Yaw Analysis");
			display_6_8_string(0,1,"Page: 4/10  L<->R");

			if(IMU_App_Is_Ready())
			{
				euler_angles angles;
				IMU_App_Get_Angles(&angles);
				uint32_t yaw_quality = IMU_App_Get_Yaw_Quality();
				float drift_rate = IMU_App_Get_Yaw_Drift_Rate();
				const char* status_str = IMU_App_Get_Yaw_Status_String();

				// 显示当前yaw值
				display_6_8_string(0,2,"Yaw: ");
				LCD_clear_L(30,2);  // 清除数字显示区域避免残留
				display_6_8_number(30,2,(int16_t)angles.yaw);
				display_6_8_string(66,2,"deg");

				// 显示质量评分
				display_6_8_string(0,3,"Quality: ");
				LCD_clear_L(54,3);  // 清除数字显示区域避免残留
				display_6_8_number(54,3,(int16_t)yaw_quality);
				display_6_8_string(78,3,"%");

				// 显示状态
				display_6_8_string(0,4,"Status: ");
				display_6_8_string(48,4,(char*)status_str);

				// 显示漂移率
				display_6_8_string(0,5,"Drift: ");
				LCD_clear_L(42,5);  // 清除数字显示区域避免残留
				display_6_8_number(42,5,(int16_t)(drift_rate * 1000)); // 转换为m°/s
				display_6_8_string(78,5,"m°/s");

				// 显示重置提示
				display_6_8_string(0,6,"Press UP to Reset");
				display_6_8_string(0,7,"Yaw Value");
			}
			else
			{
				display_6_8_string(0,2,"IMU: Not Ready");
				display_6_8_string(0,3,"Cannot Analyze");
				display_6_8_string(0,4,"Yaw Performance");
			}
			break;
		}
		case 4:
		{
			// 显示校准数据管理页面
			display_6_8_string(0,0,"Calibration Data");
			display_6_8_string(0,1,"Page: 5/10  L<->R");

			if(IMU_App_Is_Ready())
			{
				calib_status_t calib_status = IMU_App_Get_Calibration_Status();
				const char* status_str = IMU_App_Get_Calibration_Status_String();
				uint32_t crc32 = Calibration_Get_CRC32();

				// 显示校准数据状态
				display_6_8_string(0,2,"Status: ");
				display_6_8_string(48,2,(char*)status_str);

				// 显示数据完整性
				display_6_8_string(0,3,"Integrity: ");
				if(Calibration_Verify_Integrity() == 0)
				{
					display_6_8_string(66,3,"OK");
				}
				else
				{
					display_6_8_string(66,3,"BAD");
				}

				// 显示CRC32值
				display_6_8_string(0,4,"CRC32: ");
				LCD_clear_L(42,4);  // 清除数字显示区域避免残留
				display_6_8_number(42,4,(int16_t)(crc32 & 0xFFFF));

				// 显示操作提示
				display_6_8_string(0,5,"UP: Save Data");
				display_6_8_string(0,6,"DOWN: Load Data");
				display_6_8_string(0,7,"CENTER: Reset");
			}
			else
			{
				display_6_8_string(0,2,"IMU: Not Ready");
				display_6_8_string(0,3,"Cannot Manage");
				display_6_8_string(0,4,"Calibration Data");
			}
			break;
		}
		case 5:
		{
			// 显示IMU测试验证页面
			display_6_8_string(0,0,"IMU Test Report");
			display_6_8_string(0,1,"Page: 6/10  L<->R");

			if(IMU_App_Is_Ready())
			{
				uint8_t test_result = IMU_App_Get_Test_Results();
				const char* test_status = IMU_App_Get_Test_Status_String();
				float precision, drift_rate;
				uint32_t quality;
				uint8_t stability;

				IMU_App_Get_Performance_Stats(&precision, &drift_rate, &quality, &stability);

				// 显示测试状态
				display_6_8_string(0,2,"Status: ");
				display_6_8_string(48,2,(char*)test_status);

				// 显示性能指标
				display_6_8_string(0,3,"Precision: ");
				LCD_clear_L(66,3);  // 清除数字显示区域避免残留
				display_6_8_number(66,3,(int16_t)(precision * 10)); // 0.1°精度
				display_6_8_string(90,3,"0.1°");

				display_6_8_string(0,4,"Drift: ");
				LCD_clear_L(42,4);  // 清除数字显示区域避免残留
				display_6_8_number(42,4,(int16_t)(drift_rate * 1000)); // m°/s
				display_6_8_string(78,4,"m°/s");

				display_6_8_string(0,5,"Quality: ");
				LCD_clear_L(54,5);  // 清除数字显示区域避免残留
				display_6_8_number(54,5,(int16_t)quality);
				display_6_8_string(78,5,"%");

				// 显示稳定性状态
				display_6_8_string(0,6,"Stability: ");
				if(stability)
				{
					display_6_8_string(66,6,"Good");
				}
				else
				{
					display_6_8_string(66,6,"Poor");
				}

				// 显示操作提示
				display_6_8_string(0,7,"UP: Start Test");
			}
			else
			{
				display_6_8_string(0,2,"IMU: Not Ready");
				display_6_8_string(0,3,"Cannot Perform");
				display_6_8_string(0,4,"Test Validation");
			}
			break;
		}
		case 6:
		{
			// 显示字体测试页面
			display_6_8_string(0,0,"Font Test Page");
			display_6_8_string(0,1,"Page: 7/10  L<->R");
			display_6_8_string(0,2,"ABCDEFGHIJKLMNOP");
			display_6_8_string(0,3,"QRSTUVWXYZ");
			display_6_8_string(0,4,"abcdefghijklmnop");
			display_6_8_string(0,5,"qrstuvwxyz");
			display_6_8_string(0,6,"0123456789");
			display_6_8_string(0,7,"!@#$%^&*()_+-=");
			break;
		}
		case 7:
		{
			// 显示编码器转速页面
			display_6_8_string(0,0,"Encoder Speed");
			display_6_8_string(0,1,"Page: 8/10  L<->R");

			// 显示左轮转速 - 先清除数字区域避免残留
			display_6_8_string(0,2,"Left Wheel:");
			LCD_clear_L(72,2);  // 清除数字显示区域
			display_6_8_number(72,2,(int16_t)NEncoder.left_motor_speed_rpm);
			display_6_8_string(108,2,"RPM");

			// 显示右轮转速 - 先清除数字区域避免残留
			display_6_8_string(0,3,"Right Wheel:");
			LCD_clear_L(78,3);  // 清除数字显示区域
			display_6_8_number(78,3,(int16_t)NEncoder.right_motor_speed_rpm);
			display_6_8_string(114,3,"RPM");

			// 显示左轮线速度 - 先清除数字区域避免残留
			display_6_8_string(0,4,"Left Speed:");
			LCD_clear_L(66,4);  // 清除数字显示区域
			display_6_8_number(66,4,(int16_t)NEncoder.left_motor_speed_cmps);
			display_6_8_string(102,4,"cm/s");

			// 显示右轮线速度 - 先清除数字区域避免残留
			display_6_8_string(0,5,"Right Speed:");
			LCD_clear_L(72,5);  // 清除数字显示区域
			display_6_8_number(72,5,(int16_t)NEncoder.right_motor_speed_cmps);
			display_6_8_string(108,5,"cm/s");

			// 显示编码器计数 - 先清除数字区域避免残留
			display_6_8_string(0,6,"L_cnt:");
			LCD_clear_L(36,6);  // 清除左计数显示区域
			display_6_8_number(36,6,NEncoder.left_motor_cnt);
			display_6_8_string(72,6,"R_cnt:");
			LCD_clear_L(108,6); // 清除右计数显示区域
			display_6_8_number(108,6,NEncoder.right_motor_cnt);

			// 显示方向 - 先清除数字区域避免残留
			display_6_8_string(0,7,"Dir: L");
			LCD_clear_L(42,7);  // 清除左方向显示区域
			display_6_8_number(42,7,NEncoder.left_motor_dir);
			display_6_8_string(66,7," R");
			LCD_clear_L(78,7);  // 清除右方向显示区域
			display_6_8_number(78,7,NEncoder.right_motor_dir);
			break;
		}
		case 8:
		{
			// 显示数字测试页面
			display_6_8_string(0,0,"Number Test Page");
			display_6_8_string(0,1,"Page: 9/10  L<->R");
			display_6_8_string(0,2,"Counter:");
			LCD_clear_L(60,2);  // 清除数字显示区域避免残留
			display_6_8_number(60,2,display_counter);
			display_6_8_string(0,3,"Float: ");
			LCD_clear_L(42,3);  // 清除数字显示区域避免残留
			display_6_8_number(42,3,3.14159f);
			display_6_8_string(0,4,"Hex: 0x");
			LCD_clear_L(48,4);  // 清除数字显示区域避免残留
			display_6_8_number(48,4,0xABCD);
			display_6_8_string(0,5,"Binary: ");
			LCD_clear_L(54,5);  // 清除数字显示区域避免残留
			display_6_8_number(54,5,0b1010);
			display_6_8_string(0,6,"Time: ");
			LCD_clear_L(36,6);  // 清除数字显示区域避免残留
			display_6_8_number(36,6,display_counter/1000);
			display_6_8_string(60,6,"sec");
			display_6_8_string(0,7,"LED Test Complete");
			break;
		}
		case 9:
		{
			// 显示GPIO引脚状态页面
			display_6_8_string(0,0,"GPIO Pin Status");
			display_6_8_string(0,1,"Page: 10/10  L<->R");

			// 更新GPIO状态
			HUI_Read_All_Pins();

			// 显示12个引脚状态，每行2个引脚
			// 第2行：PA31和PA28状态
			display_6_8_string(0,2,"PA31:");
			display_6_8_number(30,2,HUI_Get_Pin_State(HUI_PIN_PA31_INDEX));
			display_6_8_string(60,2,"PA28:");
			display_6_8_number(90,2,HUI_Get_Pin_State(HUI_PIN_PA28_INDEX));

			// 第3行：PA1和PA0状态
			display_6_8_string(0,3,"PA1:");
			display_6_8_number(24,3,HUI_Get_Pin_State(HUI_PIN_PA1_INDEX));
			display_6_8_string(60,3,"PA0:");
			display_6_8_number(84,3,HUI_Get_Pin_State(HUI_PIN_PA0_INDEX));

			// 第4行：PA25和PA24状态
			display_6_8_string(0,4,"PA25:");
			display_6_8_number(30,4,HUI_Get_Pin_State(HUI_PIN_PA25_INDEX));
			display_6_8_string(60,4,"PA24:");
			display_6_8_number(90,4,HUI_Get_Pin_State(HUI_PIN_PA24_INDEX));

			// 第5行：PB24和PB23状态
			display_6_8_string(0,5,"PB24:");
			display_6_8_number(30,5,HUI_Get_Pin_State(HUI_PIN_PB24_INDEX));
			display_6_8_string(60,5,"PB23:");
			display_6_8_number(90,5,HUI_Get_Pin_State(HUI_PIN_PB23_INDEX));

			// 第6行：PB19和PB18状态
			display_6_8_string(0,6,"PB19:");
			display_6_8_number(30,6,HUI_Get_Pin_State(HUI_PIN_PB19_INDEX));
			display_6_8_string(60,6,"PB18:");
			display_6_8_number(90,6,HUI_Get_Pin_State(HUI_PIN_PB18_INDEX));

			// 第7行：PA16和PB13状态
			display_6_8_string(0,7,"PA16:");
			display_6_8_number(30,7,HUI_Get_Pin_State(HUI_PIN_PA16_INDEX));
			display_6_8_string(60,7,"PB13:");
			display_6_8_number(90,7,HUI_Get_Pin_State(HUI_PIN_PB13_INDEX));
			break;
		}
		default:
			page_number = 0;
			break;
	}
}
