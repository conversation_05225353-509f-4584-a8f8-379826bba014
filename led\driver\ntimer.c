#include "ti_msp_dl_config.h"
#include "system.h"
#include "buzzer_app.h"
#include "../apply/imu_app.h"  // IMU应用层头文件

#include "ntimer.h"



systime timer_t1a,timer_t5a,timer_t6a,timer_t7a;


void timer_irq_config(void)
{
  //pwm
  DL_TimerA_startCounter(PWM_0_INST);
  DL_TimerA_startCounter(PWM_1_INST);
  DL_TimerG_startCounter(PWM_2_INST);
	
  NVIC_EnableIRQ(TIMER_G0_INST_INT_IRQN);
  DL_TimerG_startCounter(TIMER_G0_INST);
  NVIC_EnableIRQ(TIMER_G6_INST_INT_IRQN);
  DL_TimerG_startCounter(TIMER_G6_INST);
	
  NVIC_EnableIRQ(TIMER_G8_INST_INT_IRQN);
  DL_TimerG_startCounter(TIMER_G8_INST);
	
  NVIC_EnableIRQ(TIMER_G12_INST_INT_IRQN);
  DL_TimerG_startCounter(TIMER_G12_INST);
}

void timer_pwm_config(void)
{
  //pwm
  DL_TimerA_startCounter(PWM_0_INST);//A0
  DL_TimerA_startCounter(PWM_1_INST);//A1
  DL_TimerG_startCounter(PWM_2_INST);//G7
}




systime t_g0;
void TIMER_G0_INST_IRQHandler(void)
{
  switch (DL_TimerG_getPendingInterrupt(TIMER_G0_INST))
  {
    case DL_TIMERG_IIDX_ZERO:
    {
      get_systime(&t_g0);
  
    }
    break;

    default:
      break;
  }
}


systime t_g1;
void TIMER_G6_INST_IRQHandler(void)//����վ���ݷ����жϺ���
{
  switch (DL_TimerG_getPendingInterrupt(TIMER_G6_INST))
  {
    case DL_TIMERG_IIDX_ZERO:
    {
      get_systime(&t_g1);
      // LED显示工程简化版本 - 移除复杂控制逻辑
      Buzzer_Work(&buzzer);  // 蜂鸣器工作函数 - 支持按键提示音
    }
    break;

    default:
      break;
  }
}

systime t_g8;
void TIMER_G8_INST_IRQHandler(void)//����վ���ݷ����жϺ���
{
  switch (DL_TimerG_getPendingInterrupt(TIMER_G8_INST))
  {
    case DL_TIMERG_IIDX_ZERO:
    {
      get_systime(&t_g8);
      // LED显示工程简化版本 - 移除复杂控制逻辑
    }
    break;
    default:
      break;
  }
}


systime t_g12;
void TIMER_G12_INST_IRQHandler(void)//����վ���ݷ����жϺ���
{
  switch (DL_TimerG_getPendingInterrupt(TIMER_G12_INST))
  {
    case DL_TIMERG_IIDX_ZERO:
    {
      get_systime(&t_g12);
      // LED显示工程简化版本 - 移除复杂控制逻辑
      IMU_App_Update();  // IMU数据更新(5ms周期)
    }
    break;
    default:
      break;
  }
}



void Reserved_PWM5_Output(uint16_t us)
{
  DL_TimerA_setCaptureCompareValue(PWM_1_INST, us, GPIO_PWM_1_C0_IDX);//TIMA1-CH0-PA15
}

void Reserved_PWM6_Output(uint16_t us)
{
  DL_TimerA_setCaptureCompareValue(PWM_1_INST, us, GPIO_PWM_1_C1_IDX);//TIMA1-CH1-PA16
}

void Reserved_PWM7_Output(uint16_t us)
{
  DL_TimerG_setCaptureCompareValue(PWM_2_INST, us, GPIO_PWM_2_C0_IDX);//TIMG7-CH0-PA17
}

void Reserved_PWM8_Output(uint16_t us)
{
  DL_TimerG_setCaptureCompareValue(PWM_2_INST, us, GPIO_PWM_2_C1_IDX);//TIMG7-CH0-PA2
}


