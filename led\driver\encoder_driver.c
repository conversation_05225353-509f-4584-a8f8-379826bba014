#include "ti_msp_dl_config.h"
#include "encoder_driver.h"
#include "filter.h"

// 编码器数据结构实例
encoder NEncoder = {
    .left_motor_period_ms = 20,
    .right_motor_period_ms = 20,
};

// 速度滤波器变量
static lpf_buf left_speed_filter_buf = {0};
static lpf_buf right_speed_filter_buf = {0};
static lpf_param speed_filter_param = {0};
static uint8_t filter_initialized = 0;

// 电机配置参数 - 修复：使用原始项目的正确参数
motor_config trackless_motor = {
    .left_encoder_dir_config = 0,
    .right_encoder_dir_config = 0,
    .left_motion_dir_config = 0,
    .right_motion_dir_config = 0,
    .wheel_radius_cm = 2.40f,        // 修复：轮胎半径2.4cm（与原始项目一致）
    .pulse_num_per_circle = 1060,    // 修复：每圈脉冲数1060（与原始项目一致）
    .servo_median_value1 = 1500,
    .servo_median_value2 = 1500,
};

void Encoder_Init(void)
{
    // 清除中断挂起状态
    NVIC_ClearPendingIRQ(PORTB_INT_IRQN);
    // 使能GPIOB中断
    NVIC_EnableIRQ(PORTB_INT_IRQN);
}

void QEI0_IRQHandler(void)  // 右轮编码器中断（现在处理左轮物理信号）
{
    // 修复：反转方向逻辑，使前转为正方向，后转为负方向
    NEncoder.right_motor_dir = ((PORTB_PORT->DIN31_0 & PORTB_LEFT_DIR_PIN) != 0 ? 1 : -1);

    if(trackless_motor.right_encoder_dir_config == 0) {
        if(NEncoder.right_motor_dir == 1) {
            NEncoder.right_motor_period_cnt++;
        } else {
            NEncoder.right_motor_period_cnt--;
        }
        NEncoder.right_motor_total_cnt += NEncoder.right_motor_dir;
    }
}

void QEI1_IRQHandler(void)  // 左轮编码器中断（现在处理右轮物理信号）
{
    // 修复：反转方向逻辑，使前转为正方向，后转为负方向
    NEncoder.left_motor_dir = ((PORTB_PORT->DIN31_0 & PORTB_RIGHT_DIR_PIN) != 0 ? -1 : 1);

    if(trackless_motor.left_encoder_dir_config == 0) {
        if(NEncoder.left_motor_dir == 1) {
            NEncoder.left_motor_period_cnt++;
        } else {
            NEncoder.left_motor_period_cnt--;
        }
        NEncoder.left_motor_total_cnt += NEncoder.left_motor_dir;
    }
}

float get_left_motor_speed(void)
{
    static uint16_t cnt1 = 0;
    cnt1++;
    if(cnt1 >= 4)  // 修复：改为每4次调用更新一次，与原始项目一致
    {
        cnt1 = 0;
        NEncoder.left_motor_period_ms = 20;

        // 初始化滤波器（仅执行一次）
        if(!filter_initialized)
        {
            // 设置滤波器参数：采样频率50Hz(每20ms)，截止频率10Hz
            set_cutoff_frequency(50.0f, 10.0f, &speed_filter_param);
            filter_initialized = 1;
        }

        // 计算转速(RPM) - 使用原始项目的公式
        NEncoder.left_motor_speed_rpm = 60 * (NEncoder.left_motor_period_cnt * 1.0f / trackless_motor.pulse_num_per_circle)
                                       / (NEncoder.left_motor_period_ms * 0.001f);

        // 计算线速度(cm/s) - 使用原始项目的公式
        float raw_speed = 2 * 3.14159f * trackless_motor.wheel_radius_cm * (NEncoder.left_motor_speed_rpm / 60.0f);

        // 应用低通滤波器
        NEncoder.left_motor_speed_cmps = LPButterworth(raw_speed, &left_speed_filter_buf, &speed_filter_param);

        NEncoder.left_motor_period_cnt = 0;  // 清零计数
    }
    return NEncoder.left_motor_speed_cmps;
}

float get_right_motor_speed(void)
{
    static uint16_t cnt2 = 0;
    cnt2++;
    if(cnt2 >= 4)  // 修复：改为每4次调用更新一次，与原始项目一致
    {
        cnt2 = 0;
        NEncoder.right_motor_period_ms = 20;

        // 计算转速(RPM) - 使用原始项目的公式
        NEncoder.right_motor_speed_rpm = 60 * (NEncoder.right_motor_period_cnt * 1.0f / trackless_motor.pulse_num_per_circle)
                                        / (NEncoder.right_motor_period_ms * 0.001f);

        // 计算线速度(cm/s) - 使用原始项目的公式
        float raw_speed = 2 * 3.14159f * trackless_motor.wheel_radius_cm * (NEncoder.right_motor_speed_rpm / 60.0f);

        // 应用低通滤波器
        NEncoder.right_motor_speed_cmps = LPButterworth(raw_speed, &right_speed_filter_buf, &speed_filter_param);

        NEncoder.right_motor_period_cnt = 0;  // 清零计数
    }
    return NEncoder.right_motor_speed_cmps;
}

void get_wheel_speed(void)
{
    // 修复：直接调用速度计算函数，内部已有采样控制逻辑
    get_left_motor_speed();
    get_right_motor_speed();
}
