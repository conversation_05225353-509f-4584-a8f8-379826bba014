#ifndef __FILTER_H
#define __FILTER_H

#include <stdint.h>

// Butterworth低通滤波器缓存结构
typedef struct
{
    float input[3];  // 输入历史数据 x(n-2), x(n-1), x(n)
    float output[3]; // 输出历史数据 y(n-2), y(n-1), y(n)
} lpf_buf;

// Butterworth低通滤波器参数结构
typedef struct
{
    float a[3]; // 分母系数 a0, a1, a2
    float b[3]; // 分子系数 b0, b1, b2
} lpf_param;

// 函数声明
float LPButterworth(float curr_input, lpf_buf *buf, lpf_param *params); // Butterworth低通滤波器
void set_cutoff_frequency(float sample_frequent, float cutoff_frequent, lpf_param *LPF); // 设置截止频率

#endif
